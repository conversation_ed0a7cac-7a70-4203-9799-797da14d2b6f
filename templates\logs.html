{% extends 'base.html' %}

{% block title %}Sistem Logları - ISG Form Yönetim Sistemi{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="mb-1">Sistem Logları</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Kontrol Paneli</a></li>
                        <li class="breadcrumb-item active">Sistem Logları</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><PERSON><PERSON></h5>
            </div>
            <div class="card-body">
                <form method="get" action="{{ url_for('logs') }}" id="logFilterForm">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="log_type" class="form-label">Log Türü</label>
                            <select class="form-select" id="log_type" name="log_type">
                                <option value="">Tümü</option>
                                <option value="LOGIN" {% if request.args.get('log_type') == 'LOGIN' %}selected{% endif %}>Giriş</option>
                                <option value="LOGOUT" {% if request.args.get('log_type') == 'LOGOUT' %}selected{% endif %}>Çıkış</option>
                                <option value="FORM_CREATE" {% if request.args.get('log_type') == 'FORM_CREATE' %}selected{% endif %}>Form Oluşturma</option>
                                <option value="FORM_UPDATE" {% if request.args.get('log_type') == 'FORM_UPDATE' %}selected{% endif %}>Form Güncelleme</option>
                                <option value="FORM_ASSIGNMENT" {% if request.args.get('log_type') == 'FORM_ASSIGNMENT' %}selected{% endif %}>Form Atama</option>
                                <option value="USER_CREATE" {% if request.args.get('log_type') == 'USER_CREATE' %}selected{% endif %}>Kullanıcı Oluşturma</option>
                                <option value="USER_UPDATE" {% if request.args.get('log_type') == 'USER_UPDATE' %}selected{% endif %}>Kullanıcı Güncelleme</option>
                                <option value="USER_STATUS" {% if request.args.get('log_type') == 'USER_STATUS' %}selected{% endif %}>Kullanıcı Durumu</option>
                                <option value="USER_PASSWORD" {% if request.args.get('log_type') == 'USER_PASSWORD' %}selected{% endif %}>Şifre Sıfırlama</option>
                                <option value="FIELD_UPDATE" {% if request.args.get('log_type') == 'FIELD_UPDATE' %}selected{% endif %}>Alan Güncelleme</option>
                                <option value="ERROR" {% if request.args.get('log_type') == 'ERROR' %}selected{% endif %}>Hata</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="user_id" class="form-label">Kullanıcı</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="">Tümü</option>
                                {% for user in users %}
                                <option value="{{ user.ID }}" {% if request.args.get('user_id')|int == user.ID %}selected{% endif %}>
                                    {{ user.NAME }} {{ user.SURNAME }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="start_date" class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request.args.get('start_date', '') }}">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="end_date" class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request.args.get('end_date', '') }}">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="limit" class="form-label">Gösterilecek Kayıt Sayısı</label>
                            <select class="form-select" id="limit" name="limit">
                                <option value="100" {% if request.args.get('limit', '100') == '100' %}selected{% endif %}>100</option>
                                <option value="250" {% if request.args.get('limit') == '250' %}selected{% endif %}>250</option>
                                <option value="500" {% if request.args.get('limit') == '500' %}selected{% endif %}>500</option>
                                <option value="1000" {% if request.args.get('limit') == '1000' %}selected{% endif %}>1000</option>
                            </select>
                        </div>
                        <div class="col-md-9 d-flex align-items-end mb-3">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-filter me-2"></i>Filtrele
                            </button>
                            <a href="{{ url_for('logs') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-2"></i>Filtreleri Temizle
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6 mb-2 mb-md-0">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-list-ul me-2 text-primary"></i>
                            <h5 class="mb-0">Log Kayıtları</h5>
                            <span class="badge bg-primary rounded-pill ms-2">{{ logs|length if logs else 0 }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-search text-muted"></i>
                            </span>
                            <input type="text" id="searchInput" class="form-control border-start-0" placeholder="Ara...">
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if logs %}
                <div class="table-responsive">
                    <table class="table table-hover" id="logsTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Tarih/Saat</th>
                                <th>Kullanıcı</th>
                                <th>İşlem Türü</th>
                                <th>Açıklama</th>
                                <th>IP Adresi</th>
                                <th>İlgili ID</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in logs %}
                            <tr>
                                <td>{{ log.ID }}</td>
                                <td>{{ log.CREATED_AT|format_datetime }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="avatar-sm bg-light text-primary me-2">
                                            {{ log.user_fullname[:1] if log.user_fullname else log.USERNAME[:1] if log.USERNAME else '?' }}
                                        </span>
                                        <span>{{ log.user_fullname if log.user_fullname else log.USERNAME }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge 
                                        {% if log.LOG_TYPE == 'LOGIN' %}bg-success
                                        {% elif log.LOG_TYPE == 'LOGOUT' %}bg-secondary
                                        {% elif log.LOG_TYPE == 'FORM_CREATE' %}bg-primary
                                        {% elif log.LOG_TYPE == 'FORM_UPDATE' %}bg-info
                                        {% elif log.LOG_TYPE == 'USER_CREATE' %}bg-primary
                                        {% elif log.LOG_TYPE == 'USER_STATUS' %}bg-warning
                                        {% elif log.LOG_TYPE == 'USER_PASSWORD' %}bg-info
                                        {% elif log.LOG_TYPE == 'FIELD_UPDATE' %}bg-info
                                        {% elif log.LOG_TYPE == 'ERROR' %}bg-danger
                                        {% else %}bg-secondary{% endif %}">
                                        {{ log.LOG_TYPE }}
                                    </span>
                                </td>
                                <td>{{ log.LOG_DESCRIPTION }}</td>
                                <td>{{ log.IP_ADDRESS }}</td>
                                <td>
                                    {% if log.RELATED_ID %}
                                    {% if log.LOG_TYPE in ['FORM_CREATE', 'FORM_UPDATE', 'FIELD_UPDATE'] %}
                                    <a href="{{ url_for('form_detail', form_id=log.RELATED_ID) }}" class="badge bg-light text-dark">
                                        Form #{{ log.RELATED_ID }}
                                    </a>
                                    {% elif log.LOG_TYPE in ['USER_CREATE', 'USER_STATUS', 'USER_PASSWORD'] %}
                                    <span class="badge bg-light text-dark">
                                        Kullanıcı #{{ log.RELATED_ID }}
                                    </span>
                                    {% else %}
                                    {{ log.RELATED_ID }}
                                    {% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-list-ul text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-3">Henüz log kaydı bulunmamaktadır.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Log arama işlevi
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchText = searchInput.value.toLowerCase();
                const logsTable = document.getElementById('logsTable');

                if (logsTable) {
                    const tbody = logsTable.querySelector('tbody');
                    if (tbody) {
                        const rows = tbody.getElementsByTagName('tr');
                        for (let i = 0; i < rows.length; i++) {
                            const rowText = rows[i].textContent.toLowerCase();
                            if (rowText.includes(searchText)) {
                                rows[i].style.display = '';
                            } else {
                                rows[i].style.display = 'none';
                            }
                        }
                    }
                }
            });
        }
    });
</script>
{% endblock %}
