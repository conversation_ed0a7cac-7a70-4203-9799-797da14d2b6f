# ISG Form Yönetim Sistemi

Bu proje, <PERSON>ş Sağlığı ve Güvenliği (ISG) formlarının yönetimi için geliştirilmiş kapsamlı bir web uygulamasıdır. Flask ve Bootstrap kullanılarak oluşturulmuştur.

## Özellikler

### Kullanıcı Yönetimi
- Kullanıcı girişi ve çıkışı
- Üç farklı yetki seviyesi: ADMIN, USER, NULL
- Kullanıcı ekleme, düzenleme ve devre dışı bırakma (admin)
- Kullanıcı şifre sıfırlama (admin)

### Form İşlemleri
- ISG formlarını listeleme ve filtreleme
- Yeni ISG formu oluşturma
- Form detaylarını görüntüleme
- Form durumunu güncelleme ve atama yapma
- Form geçmişini görüntüleme
- Form alanlarını düzenleme (admin)

### Resim İşlemleri
- Form resimlerini görüntüleme
- Gelişmiş thumbnail görünümü
- Resimleri büyük boyutta açma

### Sistem Logları
- Tüm kullanıcı işlemlerini loglama
- Log kayıtlarını filtreleme ve arama
- Detaylı log bilgileri görüntüleme

## Kurulum

1. Gerekli Python paketlerini yükleyin:
   ```
   pip install -r requirements.txt
   ```

2. `.env` dosyasını düzenleyin:
   ```
   DB_SERVER=sunucu_adı\SQLEXPRESS
   DB_NAME=veritabanı_adı
   DB_USER=kullanıcı_adı
   DB_PASSWORD=şifre
   SECRET_KEY=gizli-anahtar-buraya
   DEBUG=True
   DEFAULT_ADMIN_ID=1
   ```

3. Uygulamayı çalıştırın:
   ```
   python app.py
   ```

4. Tarayıcınızda `http://localhost:4000` adresine gidin.

## Veritabanı Yapısı

Uygulama, aşağıdaki tablolara sahip bir SQL Server veritabanı kullanır:

- `USERS`: Kullanıcı bilgileri
- `ISG_FORMS`: ISG formları
- `FORM_ASSIGNMENTS`: Form atamaları
- `ISG_PICTURE`: Form resimleri
- `FORM_TYPE`: Form tipleri
- `USER_LOGS`: Sistem log kayıtları

## Jira Benzeri İş Akışı

Sistem, Jira benzeri bir iş akışı mantığıyla çalışır:

1. Bir kullanıcı yeni bir form oluşturur
2. Form otomatik olarak admin kullanıcısına atanır ve durumu "BEKLEMEDE" olarak ayarlanır
3. Admin kullanıcısı formu inceleyip uygun kişilere atayabilir, durumunu değiştirebilir
4. Her değişiklik, ana kayda bağlı yeni bir kayıt olarak tutulur
5. Formun tüm yaşam döngüsü izlenebilir

## Kullanıcı Yetkileri

### ADMIN Kullanıcı Özellikleri
- Tüm formları görüntüleme ve filtreleme
- Yeni form oluşturma
- Form durumunu güncelleme
- Form alanlarını düzenleme (form tipi, olay yeri)
- Kullanıcı yönetimi
- Sistem loglarını görüntüleme ve filtreleme

### USER Kullanıcı Özellikleri
- Tüm formları görüntüleme ve filtreleme
- Yeni form oluşturma
- Form durumunu güncelleme

### NULL Kullanıcı Özellikleri
- Sisteme giriş yapamaz

## Dosya Depolama

- Formlarla ilgili resimler `C:\BAYRAKTAR\ISG\MOBILE\pic\` dizininde saklanır
- Küçük resimler (thumbnails) `C:\BAYRAKTAR\ISG\MOBILE\pic\thumbnails` dizininde saklanır

## Sistem Logları

Sistem, aşağıdaki işlemleri otomatik olarak loglar:

- **LOGIN:** Kullanıcı girişi
- **LOGOUT:** Kullanıcı çıkışı
- **FORM_CREATE:** Form oluşturma
- **FORM_UPDATE:** Form güncelleme
- **FORM_ASSIGNMENT:** Form atama
- **USER_CREATE:** Kullanıcı oluşturma
- **USER_UPDATE:** Kullanıcı güncelleme
- **USER_STATUS:** Kullanıcı durumu değiştirme
- **USER_PASSWORD:** Kullanıcı şifre sıfırlama
- **FIELD_UPDATE:** Form alanı güncelleme
- **ERROR:** Hata durumları

## Detaylı Kullanıcı Kılavuzu

Sistemin tüm özelliklerini içeren detaylı bir kullanıcı kılavuzu için `ISG_Form_Yonetim_Sistemi_Kullanici_Kilavuzu.md` dosyasına bakabilirsiniz.
