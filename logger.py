"""
ISG Form Yönetim <PERSON> - Log <PERSON> modül, kullan<PERSON><PERSON><PERSON> işlemlerini loglamak için kullanılır.
"""

import db
from datetime import datetime
import json
from flask import request, session

# Log türleri
LOG_TYPE_LOGIN = "LOGIN"
LOG_TYPE_LOGOUT = "LOGOUT"
LOG_TYPE_FORM_CREATE = "FORM_CREATE"
LOG_TYPE_FORM_UPDATE = "FORM_UPDATE"
LOG_TYPE_FORM_ASSIGNMENT = "FORM_ASSIGNMENT"
LOG_TYPE_USER_CREATE = "USER_CREATE"
LOG_TYPE_USER_UPDATE = "USER_UPDATE"
LOG_TYPE_USER_STATUS = "USER_STATUS"
LOG_TYPE_USER_PASSWORD = "USER_PASSWORD"
LOG_TYPE_FIELD_UPDATE = "FIELD_UPDATE"
LOG_TYPE_ERROR = "ERROR"
LOG_TYPE_SYSTEM_ACTION = "SYSTEM_ACTION"
LOG_TYPE_REMINDER = "REMINDER"

def create_log_table():
    """Log tablosunu oluşturur"""
    try:
        conn = db.get_db_connection()
        if not conn:
            print("Veritabanı bağlantısı kurulamadı.")
            return False

        cursor = conn.cursor()

        # USER_LOGS tablosunu oluştur
        cursor.execute("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='USER_LOGS' AND xtype='U')
        CREATE TABLE USER_LOGS (
            ID INT IDENTITY(1,1) PRIMARY KEY,
            USER_ID INT,
            USERNAME NVARCHAR(255),
            LOG_TYPE NVARCHAR(50),
            LOG_DESCRIPTION NVARCHAR(MAX),
            IP_ADDRESS NVARCHAR(50),
            USER_AGENT NVARCHAR(MAX),
            RELATED_ID INT,
            ADDITIONAL_DATA NVARCHAR(MAX),
            CREATED_AT DATETIME DEFAULT GETDATE()
        )
        """)

        conn.commit()
        conn.close()
        print("Log tablosu başarıyla oluşturuldu.")
        return True
    except Exception as e:
        print(f"Log tablosu oluşturma hatası: {e}")
        return False

def add_log(user_id, username, log_type, log_description, related_id=None, additional_data=None):
    """Yeni bir log kaydı ekler"""
    try:
        # IP adresi ve user agent bilgilerini al
        ip_address = request.remote_addr if request else "N/A"
        user_agent = request.user_agent.string if request and request.user_agent else "N/A"

        # Ek verileri JSON formatına dönüştür
        if additional_data and isinstance(additional_data, dict):
            additional_data = json.dumps(additional_data)

        # Veritabanı bağlantısı
        conn = db.get_db_connection()
        if not conn:
            print("Veritabanı bağlantısı kurulamadı.")
            return False

        cursor = conn.cursor()

        # Log kaydı ekle
        query = """
        INSERT INTO USER_LOGS (USER_ID, USERNAME, LOG_TYPE, LOG_DESCRIPTION, IP_ADDRESS, USER_AGENT, RELATED_ID, ADDITIONAL_DATA, CREATED_AT)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, GETDATE())
        """

        cursor.execute(query, (user_id, username, log_type, log_description, ip_address, user_agent, related_id, additional_data))
        conn.commit()
        conn.close()

        return True
    except Exception as e:
        print(f"Log ekleme hatası: {e}")
        return False

def log_login(user_id, username, success=True):
    """Kullanıcı girişini loglar"""
    description = f"Kullanıcı başarıyla giriş yaptı: {username}" if success else f"Kullanıcı giriş denemesi başarısız: {username}"
    additional_data = {"success": success}
    return add_log(user_id, username, LOG_TYPE_LOGIN, description, additional_data=additional_data)

def log_logout(user_id, username):
    """Kullanıcı çıkışını loglar"""
    description = f"Kullanıcı çıkış yaptı: {username}"
    return add_log(user_id, username, LOG_TYPE_LOGOUT, description)

def log_form_create(user_id, username, form_id, form_type):
    """Form oluşturma işlemini loglar"""
    description = f"Kullanıcı yeni bir form oluşturdu: {form_type}"
    additional_data = {"form_id": form_id, "form_type": form_type}
    return add_log(user_id, username, LOG_TYPE_FORM_CREATE, description, related_id=form_id, additional_data=additional_data)

def log_form_update(user_id, username, form_id, new_status, assigned_to):
    """Form güncelleme işlemini loglar"""
    description = f"Kullanıcı form durumunu güncelledi: {new_status}"
    additional_data = {"form_id": form_id, "new_status": new_status, "assigned_to": assigned_to}
    return add_log(user_id, username, LOG_TYPE_FORM_UPDATE, description, related_id=form_id, additional_data=additional_data)

def log_user_create(user_id, username, created_user_id, created_username, permission):
    """Kullanıcı oluşturma işlemini loglar"""
    description = f"Kullanıcı yeni bir kullanıcı oluşturdu: {created_username} ({permission})"
    additional_data = {"created_user_id": created_user_id, "created_username": created_username, "permission": permission}
    return add_log(user_id, username, LOG_TYPE_USER_CREATE, description, related_id=created_user_id, additional_data=additional_data)

def log_user_status_update(user_id, username, target_user_id, target_username, new_status):
    """Kullanıcı durumu güncelleme işlemini loglar"""
    status_text = "etkinleştirildi" if 'active' in new_status else "devre dışı bırakıldı"
    description = f"Kullanıcı, {target_username} kullanıcısının durumunu {status_text}"
    additional_data = {"target_user_id": target_user_id, "target_username": target_username, "new_status": new_status}
    return add_log(user_id, username, LOG_TYPE_USER_STATUS, description, related_id=target_user_id, additional_data=additional_data)

def log_user_password_reset(user_id, username, target_user_id, target_username):
    """Kullanıcı şifre sıfırlama işlemini loglar"""
    description = f"Kullanıcı, {target_username} kullanıcısının şifresini sıfırladı"
    additional_data = {"target_user_id": target_user_id, "target_username": target_username}
    return add_log(user_id, username, LOG_TYPE_USER_PASSWORD, description, related_id=target_user_id, additional_data=additional_data)

def log_user_password_change(user_id, username):
    """Kullanıcının kendi şifresini değiştirme işlemini loglar"""
    description = f"Kullanıcı kendi şifresini değiştirdi"
    additional_data = {"self_change": True}
    return add_log(user_id, username, LOG_TYPE_USER_PASSWORD, description, related_id=user_id, additional_data=additional_data)

def log_field_update(user_id, username, form_id, field_name, new_value):
    """Form alanı güncelleme işlemini loglar"""
    description = f"Kullanıcı, form alanını güncelledi: {field_name}"
    additional_data = {"form_id": form_id, "field_name": field_name, "new_value": new_value}
    return add_log(user_id, username, LOG_TYPE_FIELD_UPDATE, description, related_id=form_id, additional_data=additional_data)

def log_error(user_id, username, error_message, related_id=None):
    """Hata durumunu loglar"""
    description = f"Hata oluştu: {error_message}"
    return add_log(user_id, username, LOG_TYPE_ERROR, description, related_id=related_id, additional_data={"error": error_message})

def log_system_action(user_id, username, action_type, description, related_id=None, additional_data=None):
    """Sistem işlemlerini loglar"""
    log_description = f"Sistem işlemi: {description}"
    return add_log(user_id, username, LOG_TYPE_SYSTEM_ACTION, log_description, related_id=related_id, additional_data=additional_data)

def log_reminder(reminder_type, description, success=True, related_ids=None):
    """Hatırlatıcı işlemlerini loglar"""
    log_description = f"Hatırlatıcı: {description}"
    additional_data = {
        "reminder_type": reminder_type,
        "success": success,
        "related_ids": related_ids
    }
    # Sistem kullanıcısı olarak logla (ID: 0)
    return add_log(0, "SYSTEM", LOG_TYPE_REMINDER, log_description, additional_data=additional_data)

def get_logs(limit=100, log_type=None, user_id=None, start_date=None, end_date=None):
    """Log kayıtlarını getirir"""
    try:
        # Sorgu oluştur
        query = """
        SELECT l.*, u.NAME + ' ' + u.SURNAME as user_fullname
        FROM USER_LOGS l
        LEFT JOIN USERS u ON l.USER_ID = u.ID
        WHERE 1=1
        """

        params = []

        # Filtreleri ekle
        if log_type:
            query += " AND l.LOG_TYPE = ?"
            params.append(log_type)

        if user_id:
            query += " AND l.USER_ID = ?"
            params.append(user_id)

        if start_date:
            query += " AND l.CREATED_AT >= ?"
            params.append(start_date)

        if end_date:
            query += " AND l.CREATED_AT <= ?"
            params.append(end_date)

        # Sıralama ve limit
        query += " ORDER BY l.CREATED_AT DESC"

        if limit:
            query += f" OFFSET 0 ROWS FETCH NEXT {limit} ROWS ONLY"

        # Sorguyu çalıştır
        return db.execute_query(query, params if params else None)
    except Exception as e:
        print(f"Log getirme hatası: {e}")
        return []
