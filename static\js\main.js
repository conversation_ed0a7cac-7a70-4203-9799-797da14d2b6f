// ISG Form Yönetim Sistemi JavaScript

// Sayfa yüklendiğinde çalışacak kodlar
document.addEventListener('DOMContentLoaded', function() {
    // Bootstrap tooltips'i etkinleştir
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Tablo arama fonksiyonu (formlar ve kullanıcılar için)
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchText = searchInput.value.toLowerCase();

            // Form tablosu için arama
            const formsTable = document.getElementById('formsTable');
            if (formsTable) {
                searchTable(formsTable, searchText);
            }

            // Kullanıcı tablosu için arama
            const usersTable = document.getElementById('usersTable');
            if (usersTable) {
                searchTable(usersTable, searchText);
            }
        });
    }

    // Tablo arama yardımcı fonksiyonu
    function searchTable(table, searchText) {
        const tbody = table.querySelector('tbody');
        if (tbody) {
            const rows = tbody.getElementsByTagName('tr');
            for (let i = 0; i < rows.length; i++) {
                const rowText = rows[i].textContent.toLowerCase();
                if (rowText.includes(searchText)) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        }
    }

    // Alert mesajlarını otomatik kapat
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const closeButton = alert.querySelector('.btn-close');
            if (closeButton) {
                closeButton.click();
            }
        }, 5000); // 5 saniye sonra kapat
    });

    // Kullanıcı yönetimi modalları için event listener'lar
    setupUserManagementModals();
});

// Kullanıcı yönetimi modalları için ayarlar
function setupUserManagementModals() {
    // Durum değiştirme modalı
    const statusModal = document.getElementById('statusModal');
    if (statusModal) {
        statusModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const userName = button.getAttribute('data-user-name');
            const status = button.getAttribute('data-status');

            const statusForm = document.getElementById('statusForm');
            const statusInput = document.getElementById('statusInput');
            const statusMessage = document.getElementById('statusMessage');

            statusForm.action = `/users/${userId}/status`;
            statusInput.value = status;

            if (status === 'active') {
                statusMessage.innerHTML = `<strong>${userName}</strong> kullanıcısını etkinleştirmek istediğinize emin misiniz?`;
            } else {
                statusMessage.innerHTML = `<strong>${userName}</strong> kullanıcısını devre dışı bırakmak istediğinize emin misiniz?`;
            }
        });
    }

    // Şifre sıfırlama modalı
    const passwordModal = document.getElementById('passwordModal');
    if (passwordModal) {
        passwordModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const userName = button.getAttribute('data-user-name');

            const passwordForm = document.getElementById('passwordForm');
            const passwordUserName = document.getElementById('passwordUserName');

            passwordForm.action = `/users/${userId}/password`;
            passwordUserName.innerHTML = `<strong>${userName}</strong> kullanıcısı için yeni şifre belirleyin:`;
        });
    }
}
