# MSSQL Veritabanı bağlantısı için pyodbc kullanılıyor
import pyodbc
from config import DB_SERVER, DB_NAME, DB_USER, DB_PASSWORD
from datetime import datetime
import notifications

def get_db_connection():
    """Veritabanı bağlantısı oluşturur ve döndürür"""
    try:
        # ODBC bağlantı dizesi
        conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_SERVER};DATABASE={DB_NAME};UID={DB_USER};PWD={DB_PASSWORD}"

        # Alternatif olarak Windows kimlik doğrulaması da kullanılabilir
        # conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={DB_SERVER};DATABASE={DB_NAME};Trusted_Connection=yes;"

        conn = pyodbc.connect(conn_str)
        return conn
    except pyodbc.Error as e:
        print(f"Veritabanı bağlantı hatası: {e}")
        return None

def execute_query(query, params=None, fetch=True):
    """SQL sorgusu çalıştırır ve sonuçları döndürür"""
    conn = get_db_connection()
    if not conn:
        return None

    try:
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        if fetch:
            columns = [column[0] for column in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            return results
        else:
            conn.commit()
            return cursor.rowcount
    except pyodbc.Error as e:
        print(f"Sorgu hatası: {e}")
        return None
    finally:
        conn.close()

def get_user_by_username(username):
    """Kullanıcı adına göre kullanıcı bilgilerini getirir"""
    query = "SELECT * FROM USERS WHERE USERN = ?"
    results = execute_query(query, (username,))
    return results[0] if results else None

def get_user_by_id(user_id):
    """Kullanıcı ID'sine göre kullanıcı bilgilerini getirir"""
    query = "SELECT * FROM USERS WHERE ID = ?"
    results = execute_query(query, (user_id,))
    return results[0] if results else None

def get_all_users():
    """Tüm aktif kullanıcıları getirir (USER ve ADMIN yetkisine sahip olanlar)"""
    query = """
    SELECT ID, NAME, SURNAME, USERN, PERMISSION, STATUS
    FROM USERS
    WHERE STATUS = 'active'
    AND (PERMISSION = 'ADMIN' OR PERMISSION = 'USER')
    ORDER BY NAME
    """
    return execute_query(query)

def get_all_users_for_management():
    """Tüm kullanıcıları yönetim için getirir (aktif ve pasif dahil)"""
    try:
        # Doğrudan veritabanı bağlantısı kullanarak sorgu yap
        conn = get_db_connection()
        if not conn:
            print("Veritabanı bağlantısı kurulamadı.")
            return []

        cursor = conn.cursor()
        cursor.execute("SELECT ID, NAME, SURNAME, USERN, PERMISSION, STATUS FROM USERS ORDER BY NAME")

        # Sonuçları manuel olarak sözlük listesine dönüştür
        columns = [column[0] for column in cursor.description]
        results = []
        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))

        conn.close()

        # Eksik alanları ekle
        for user in results:
            if 'EMAIL' not in user:
                user['EMAIL'] = None
            if 'CREATED_DATE' not in user:
                user['CREATED_DATE'] = None
            if 'LAST_LOGIN_DATE' not in user:
                user['LAST_LOGIN_DATE'] = None

        return results
    except Exception as e:
        print(f"Kullanıcıları getirme hatası: {e}")
        return []

def update_user_status(user_id, new_status):
    """Kullanıcı durumunu günceller (active/disabled)"""
    try:
        # Veritabanı bağlantısını doğrudan kullan
        conn = get_db_connection()
        if not conn:
            print("Veritabanı bağlantısı kurulamadı.")
            return False

        cursor = conn.cursor()

        # STATUS alanının uzunluğunu kontrol et
        cursor.execute("SELECT CHARACTER_MAXIMUM_LENGTH FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'USERS' AND COLUMN_NAME = 'STATUS'")
        result = cursor.fetchone()
        max_length = result[0] if result else 10

        # Yeni durumu uygun uzunluğa getir
        if isinstance(new_status, str):
            if len(new_status) < max_length:
                new_status = new_status.ljust(max_length)
            elif len(new_status) > max_length:
                new_status = new_status[:max_length]

        print(f"Kullanıcı durumu güncelleniyor: ID={user_id}, Yeni Durum='{new_status}'")

        # Durumu güncelle
        cursor.execute("UPDATE USERS SET STATUS = ? WHERE ID = ?", (new_status, user_id))
        conn.commit()

        # Etkilenen satır sayısını kontrol et
        affected_rows = cursor.rowcount
        conn.close()

        print(f"Güncelleme sonucu: {affected_rows} satır etkilendi")
        return affected_rows > 0
    except Exception as e:
        print(f"Kullanıcı durumu güncelleme hatası: {e}")
        return False

def update_user_password(user_id, new_password):
    """Kullanıcı şifresini günceller"""
    query = "UPDATE USERS SET PASS = ? WHERE ID = ?"
    return execute_query(query, (new_password, user_id), fetch=False)

def create_user(name, surname, username, password, email, permission):
    """Yeni kullanıcı oluşturur"""
    query = """
    INSERT INTO USERS (NAME, SURNAME, USERN, PASS, EMAIL, PERMISSION, STATUS)
    VALUES (?, ?, ?, ?, ?, ?, 'active')
    """
    params = (name, surname, username, password, email, permission)
    result = execute_query(query, params, fetch=False)

    if result:
        # Oluşturulan kullanıcının ID'sini al
        last_user_query = "SELECT TOP 1 ID FROM USERS ORDER BY ID DESC"
        last_user = execute_query(last_user_query)
        return last_user[0]['ID'] if last_user else None

    return None

def get_form_types():
    """Aktif form tiplerini getirir"""
    query = "SELECT * FROM FORMTYPE WHERE STATUS = 'ENABLE' ORDER BY DISPLAY"
    return execute_query(query)

def get_all_forms(limit=100, user_id=None, is_admin=False):
    """
    Formları getirir
    - is_admin=True ise tüm formları getirir
    - is_admin=False ise kullanıcıya herhangi bir zamanda atanmış formları getirir
    """
    if is_admin:
        # Admin kullanıcı için tüm formları getir
        query = f"""
        SELECT TOP {limit}
            f.ID, f.TYPE, f.date, f.time, f.userid, f.username,
            f.incident_date, f.incident_loc, f.description, f.form_status as status,
            u.NAME + ' ' + u.SURNAME as user_fullname,
            assigned_user.NAME + ' ' + assigned_user.SURNAME as assigned_to_name,
            last_assignment.ASSIGNED_TO as assigned_to_id
        FROM ISG_FORMS f
        LEFT JOIN USERS u ON f.userid = u.ID
        OUTER APPLY (
            SELECT TOP 1 a.*
            FROM ISG_FORM_ASSIGNMENTS a
            WHERE a.FORM_ID = f.ID
            ORDER BY a.ID DESC
        ) as last_assignment
        LEFT JOIN USERS assigned_user ON last_assignment.ASSIGNED_TO = assigned_user.ID
        ORDER BY f.ID DESC
        """
        return execute_query(query)
    else:
        # Normal kullanıcı için kendisine atanmış formları VE kendi oluşturduğu formları getir
        query = f"""
        SELECT TOP {limit}
            f.ID, f.TYPE, f.date, f.time, f.userid, f.username,
            f.incident_date, f.incident_loc, f.description, f.form_status as status,
            u.NAME + ' ' + u.SURNAME as user_fullname,
            assigned_user.NAME + ' ' + assigned_user.SURNAME as assigned_to_name,
            last_assignment.ASSIGNED_TO as assigned_to_id
        FROM ISG_FORMS f
        LEFT JOIN USERS u ON f.userid = u.ID
        OUTER APPLY (
            SELECT TOP 1 a.*
            FROM ISG_FORM_ASSIGNMENTS a
            WHERE a.FORM_ID = f.ID
            ORDER BY a.ID DESC
        ) as last_assignment
        LEFT JOIN USERS assigned_user ON last_assignment.ASSIGNED_TO = assigned_user.ID
        WHERE (
            -- Kullanıcıya atanmış formlar
            EXISTS (
                SELECT 1 FROM ISG_FORM_ASSIGNMENTS a
                WHERE a.FORM_ID = f.ID AND a.ASSIGNED_TO = ?
            )
            OR
            -- Kullanıcının oluşturduğu formlar
            f.userid = ?
        )
        ORDER BY f.ID DESC
        """
        return execute_query(query, (user_id, user_id))

def get_form_by_id(form_id, user_id=None, is_admin=False):
    """
    Form ID'sine göre form detaylarını getirir
    - is_admin=True ise herhangi bir formu getirir
    - is_admin=False ise kullanıcıya herhangi bir zamanda atanmış formu getirir
    """
    if is_admin:
        # Admin kullanıcı için herhangi bir formu getir
        query = """
        SELECT f.*, u.NAME + ' ' + u.SURNAME as user_fullname, f.form_status as status
        FROM ISG_FORMS f
        LEFT JOIN USERS u ON f.userid = u.ID
        WHERE f.ID = ?
        """
        results = execute_query(query, (form_id,))
    else:
        # Normal kullanıcı için kendisine atanmış formu VEYA kendi oluşturduğu formu getir
        query = """
        SELECT f.*, u.NAME + ' ' + u.SURNAME as user_fullname, f.form_status as status
        FROM ISG_FORMS f
        LEFT JOIN USERS u ON f.userid = u.ID
        WHERE f.ID = ?
        AND (
            -- Kullanıcıya atanmış form
            EXISTS (
                SELECT 1 FROM ISG_FORM_ASSIGNMENTS a
                WHERE a.FORM_ID = f.ID AND a.ASSIGNED_TO = ?
            )
            OR
            -- Kullanıcının oluşturduğu form
            f.userid = ?
        )
        """
        results = execute_query(query, (form_id, user_id, user_id))

    return results[0] if results else None

def get_form_assignments(form_id):
    """Form ID'sine göre atama geçmişini getirir"""
    query = """
    SELECT a.*,
           u1.NAME + ' ' + u1.SURNAME as assigned_by_name,
           u2.NAME + ' ' + u2.SURNAME as assigned_to_name,
           u3.NAME + ' ' + u3.SURNAME as escalated_to_name
    FROM ISG_FORM_ASSIGNMENTS a
    LEFT JOIN USERS u1 ON a.ASSIGNED_BY = u1.ID
    LEFT JOIN USERS u2 ON a.ASSIGNED_TO = u2.ID
    LEFT JOIN USERS u3 ON a.ESCALATED_TO = u3.ID
    WHERE a.FORM_ID = ?
    ORDER BY a.ID DESC
    """
    return execute_query(query, (form_id,))

def create_form(form_data, user_id, admin_id):
    """Yeni bir form oluşturur ve admin kullanıcısına atar"""
    # 1. ISG_FORMS tablosuna kayıt ekle
    form_query = """
    INSERT INTO ISG_FORMS (TYPE, date, time, userid, username, duty, incident_date,
                          incident_time, incident_loc, description, form_status)
    VALUES (?, GETDATE(), GETDATE(), ?, ?, ?, ?, ?, ?, ?, 'BEKLEMEDE')
    """

    form_params = (
        form_data['type'],
        user_id,
        form_data['username'],
        form_data['duty'],
        form_data['incident_date'],
        form_data['incident_time'],
        form_data['incident_loc'],
        form_data['description']
    )

    # Form kaydını oluştur
    execute_query(form_query, form_params, fetch=False)

    # Oluşturulan formun ID'sini al
    last_form_query = "SELECT TOP 1 ID FROM ISG_FORMS ORDER BY ID DESC"
    last_form = execute_query(last_form_query)
    form_id = last_form[0]['ID'] if last_form else None

    if form_id:
        # 2. ISG_FORM_ASSIGNMENTS tablosuna atama kaydı ekle
        assignment_query = """
        INSERT INTO ISG_FORM_ASSIGNMENTS (FORM_ID, ASSIGNED_BY, ASSIGNED_TO, ASSIGNMENT_DATE,
                                         STATUS, ASSIGNMENT_DESCRIPTION, ASSIGNMENT_TYPE)
        VALUES (?, ?, ?, GETDATE(), 'BEKLEMEDE', 'Form oluşturuldu ve admin kullanıcısına atandı.', 'FORM_CREATE')
        """

        assignment_params = (form_id, user_id, admin_id)
        execute_query(assignment_query, assignment_params, fetch=False)

    return form_id

def update_form_status(form_id, new_status, assigned_to, comments, current_user_id):
    """Form durumunu günceller ve yeni bir atama kaydı oluşturur"""
    try:
        # Form bilgilerini al
        form = get_form_by_id(form_id, None, True)  # Admin yetkisiyle formu al
        if not form:
            print(f"Form bulunamadı: {form_id}")
            return None

        # Atanan kullanıcı bilgilerini al
        assigned_user = get_user_by_id(assigned_to)
        if not assigned_user:
            print(f"Atanan kullanıcı bulunamadı: {assigned_to}")
            return None

        # Atayan kullanıcı bilgilerini al
        current_user = get_user_by_id(current_user_id)
        if not current_user:
            print(f"Atayan kullanıcı bulunamadı: {current_user_id}")
            return None

        # 1. ISG_FORMS tablosundaki form_status alanını güncelle
        update_query = "UPDATE ISG_FORMS SET form_status = ? WHERE ID = ?"
        execute_query(update_query, (new_status, form_id), fetch=False)

        # 2. ISG_FORM_ASSIGNMENTS tablosuna yeni kayıt ekle
        assignment_query = """
        INSERT INTO ISG_FORM_ASSIGNMENTS (FORM_ID, ASSIGNED_BY, ASSIGNED_TO, ASSIGNMENT_DATE,
                                        STATUS, ASSIGNMENT_DESCRIPTION, ASSIGNMENT_TYPE)
        VALUES (?, ?, ?, GETDATE(), ?, ?, 'FORM_UPDATE')
        """

        # Parametre sayısı ve sıralaması SQL sorgusuyla eşleşmeli
        assignment_params = (form_id, current_user_id, assigned_to, new_status, comments)
        result = execute_query(assignment_query, assignment_params, fetch=False)
        print(f"Form güncelleme başarılı: {result}")

        # 3. Atanan kişiye e-posta gönder
        try:
            # Bildirim modülünü kullanarak e-posta gönder
            notifications.send_form_update_notification(
                form_id=form_id,
                form_type=form['TYPE'],
                new_status=new_status,
                assigned_user=assigned_user,
                current_user=current_user,
                comments=comments
            )

            # İlk güncelleme ise form oluşturan kişiye de bildirim gönder
            if form["userid"] != current_user_id:  # Form oluşturan kişi güncelleyen kişi değilse
                # Atama geçmişini kontrol et - ilk güncelleme mi?
                if len(get_form_assignments(form_id)) <= 1:  # Sadece ilk atama kaydı varsa (form oluşturma)
                    # Form oluşturan kullanıcının bilgilerini al
                    creator_user = get_user_by_id(form["userid"])
                    if creator_user:
                        notifications.send_first_update_notification(
                            form_id=form_id,
                            form_type=form['TYPE'],
                            update_type="Durum Güncellemesi",
                            creator_user=creator_user,
                            current_user=current_user,
                            details=f"Form durumu '{form['status']}' değerinden '{new_status}' değerine güncellendi."
                        )
        except Exception as email_error:
            print(f"E-posta gönderme hatası: {email_error}")
            # E-posta gönderme hatası form güncelleme işlemini etkilemesin

        # 4. Form durumu COZULDU veya TAMAMLANDI ise formu dolduran kullanıcıya da e-posta gönder
        if new_status in ["COZULDU", "TAMAMLANDI"] and form["userid"] != int(assigned_to):
            try:
                # Formu dolduran kullanıcının bilgilerini al
                creator_user = get_user_by_id(form["userid"])
                if not creator_user:
                    print(f"Formu dolduran kullanıcı bulunamadı: {form['userid']}")
                else:
                    # Bildirim modülünü kullanarak e-posta gönder
                    notifications.send_form_completion_notification(
                        form_id=form_id,
                        form_type=form['TYPE'],
                        new_status=new_status,
                        creator_user=creator_user,
                        current_user=current_user,
                        comments=comments
                    )
            except Exception as email_error:
                print(f"Formu dolduran kullanıcıya e-posta gönderme hatası: {email_error}")
                # E-posta gönderme hatası form güncelleme işlemini etkilemesin

        return result
    except Exception as e:
        print(f"Form güncelleme hatası: {e}")
        return None

def get_form_pictures(form_id):
    """Form ID'sine göre resimleri getirir"""
    query = "SELECT * FROM ISG_PICTURE WHERE FORM_ID = ? ORDER BY ID DESC"
    return execute_query(query, (form_id,))

def create_form_attachment(form_id, assignment_id, original_filename, stored_filename, file_size, file_type, user_id):
    """Form için dosya eki oluşturur"""
    query = """
    INSERT INTO ISG_FORM_ATTACHMENTS (FORM_ID, ASSIGNMENT_ID, ORIGINAL_FILENAME, STORED_FILENAME,
                                     FILE_SIZE, FILE_TYPE, UPLOADED_BY, CREATED_AT)
    VALUES (?, ?, ?, ?, ?, ?, ?, GETDATE())
    """
    params = (form_id, assignment_id, original_filename, stored_filename, file_size, file_type, user_id)
    return execute_query(query, params, fetch=False)

def get_form_attachments(form_id):
    """Form ID'sine göre dosya eklerini getirir"""
    query = """
    SELECT a.*, u.NAME + ' ' + u.SURNAME as uploaded_by_name
    FROM ISG_FORM_ATTACHMENTS a
    LEFT JOIN USERS u ON a.UPLOADED_BY = u.ID
    WHERE a.FORM_ID = ?
    ORDER BY a.ID DESC
    """
    return execute_query(query, (form_id,))

# send_email fonksiyonu notifications.py modülüne taşındı

def get_user_email(user_id):
    """Kullanıcı ID'sine göre e-posta adresini getirir"""
    query = "SELECT EMAIL FROM USERS WHERE ID = ?"
    results = execute_query(query, (user_id,))
    return results[0]['EMAIL'] if results and 'EMAIL' in results[0] else None

def get_reminder_settings():
    """Tüm hatırlatıcı ayarlarını getirir"""
    query = "SELECT * FROM REMINDER_SETTINGS ORDER BY ID"
    return execute_query(query)

def get_reminder_setting(setting_name):
    """Belirli bir hatırlatıcı ayarını getirir"""
    query = "SELECT * FROM REMINDER_SETTINGS WHERE SETTING_NAME = ?"
    results = execute_query(query, (setting_name,))
    return results[0] if results else None

def update_reminder_setting(setting_name, setting_value, updated_by):
    """Hatırlatıcı ayarını günceller"""
    query = """
    UPDATE REMINDER_SETTINGS
    SET SETTING_VALUE = ?, UPDATED_AT = GETDATE(), UPDATED_BY = ?
    WHERE SETTING_NAME = ?
    """
    return execute_query(query, (setting_value, updated_by, setting_name), fetch=False)

def toggle_reminder_setting(setting_name, enabled, updated_by):
    """Hatırlatıcı ayarını etkinleştirir veya devre dışı bırakır"""
    query = """
    UPDATE REMINDER_SETTINGS
    SET ENABLED = ?, UPDATED_AT = GETDATE(), UPDATED_BY = ?
    WHERE SETTING_NAME = ?
    """
    return execute_query(query, (1 if enabled else 0, updated_by, setting_name), fetch=False)

def get_unassigned_forms():
    """Henüz atanmamış formları getirir"""
    query = """
    SELECT f.*, u.NAME + ' ' + u.SURNAME as user_fullname
    FROM ISG_FORMS f
    LEFT JOIN USERS u ON f.userid = u.ID
    WHERE NOT EXISTS (
        SELECT 1 FROM ISG_FORM_ASSIGNMENTS a
        WHERE a.FORM_ID = f.ID AND a.ASSIGNMENT_TYPE != 'FORM_CREATE'
    )
    AND f.form_status NOT IN ('TAMAMLANDI', 'COZULDU')
    """
    return execute_query(query)

def get_pending_forms_older_than_days(days):
    """Belirli bir gün sayısından daha eski bekleyen formları getirir"""
    query = """
    SELECT f.*, u.NAME + ' ' + u.SURNAME as user_fullname,
           (SELECT TOP 1 ASSIGNED_TO FROM ISG_FORM_ASSIGNMENTS
            WHERE FORM_ID = f.ID ORDER BY ID DESC) as last_assigned_to
    FROM ISG_FORMS f
    LEFT JOIN USERS u ON f.userid = u.ID
    WHERE f.form_status NOT IN ('TAMAMLANDI', 'COZULDU')
    AND DATEDIFF(DAY, f.date, GETDATE()) >= ?
    """
    return execute_query(query, (days,))

def update_last_reminder_sent(setting_name):
    """Son hatırlatıcı gönderim zamanını günceller"""
    query = """
    UPDATE REMINDER_SETTINGS
    SET SETTING_VALUE = CONVERT(VARCHAR, GETDATE(), 120), UPDATED_AT = GETDATE()
    WHERE SETTING_NAME = ?
    """
    return execute_query(query, (setting_name,), fetch=False)

def create_tables():
    """Gerekli tabloları oluşturur"""
    try:
        # USERS tablosuna EMAIL alanı ekle (eğer yoksa)
        try:
            execute_query("""
            IF NOT EXISTS (
                SELECT * FROM sys.columns
                WHERE object_id = OBJECT_ID('USERS') AND name = 'EMAIL'
            )
            ALTER TABLE USERS ADD EMAIL NVARCHAR(255)
            """, fetch=False)
            print("USERS tablosuna EMAIL alanı eklendi.")
        except Exception as e:
            print(f"EMAIL alanı ekleme hatası: {e}")

        # USER_LOGS tablosunu oluştur (eğer yoksa)
        try:
            execute_query("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='USER_LOGS' AND xtype='U')
            CREATE TABLE USER_LOGS (
                ID INT IDENTITY(1,1) PRIMARY KEY,
                USER_ID INT,
                USERNAME NVARCHAR(255),
                LOG_TYPE NVARCHAR(50),
                LOG_DESCRIPTION NVARCHAR(MAX),
                IP_ADDRESS NVARCHAR(50),
                USER_AGENT NVARCHAR(MAX),
                RELATED_ID INT,
                ADDITIONAL_DATA NVARCHAR(MAX),
                CREATED_AT DATETIME DEFAULT GETDATE()
            )
            """, fetch=False)
            print("USER_LOGS tablosu oluşturuldu veya zaten mevcut.")
        except Exception as e:
            print(f"USER_LOGS tablosu oluşturma hatası: {e}")

        # REMINDER_SETTINGS tablosunu oluştur (eğer yoksa)
        try:
            execute_query("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='REMINDER_SETTINGS' AND xtype='U')
            CREATE TABLE REMINDER_SETTINGS (
                ID INT IDENTITY(1,1) PRIMARY KEY,
                SETTING_NAME NVARCHAR(100) NOT NULL,
                SETTING_VALUE NVARCHAR(255) NOT NULL,
                DESCRIPTION NVARCHAR(MAX),
                ENABLED BIT DEFAULT 1,
                CREATED_AT DATETIME DEFAULT GETDATE(),
                UPDATED_AT DATETIME DEFAULT GETDATE(),
                UPDATED_BY INT
            )
            """, fetch=False)

            # Varsayılan ayarları ekle (eğer yoksa)
            execute_query("""
            IF NOT EXISTS (SELECT * FROM REMINDER_SETTINGS WHERE SETTING_NAME = 'UNASSIGNED_FORM_REMINDER_EMAIL')
            INSERT INTO REMINDER_SETTINGS (SETTING_NAME, SETTING_VALUE, DESCRIPTION, ENABLED)
            VALUES ('UNASSIGNED_FORM_REMINDER_EMAIL', '<EMAIL>', 'Atanmamış formlar için bildirim gönderilecek e-posta adresi', 1)
            """, fetch=False)

            execute_query("""
            IF NOT EXISTS (SELECT * FROM REMINDER_SETTINGS WHERE SETTING_NAME = 'UNASSIGNED_FORM_REMINDER_FREQUENCY')
            INSERT INTO REMINDER_SETTINGS (SETTING_NAME, SETTING_VALUE, DESCRIPTION, ENABLED)
            VALUES ('UNASSIGNED_FORM_REMINDER_FREQUENCY', '24', 'Atanmamış formlar için bildirim gönderme sıklığı (saat)', 1)
            """, fetch=False)

            execute_query("""
            IF NOT EXISTS (SELECT * FROM REMINDER_SETTINGS WHERE SETTING_NAME = 'PENDING_FORM_REMINDER_DAYS')
            INSERT INTO REMINDER_SETTINGS (SETTING_NAME, SETTING_VALUE, DESCRIPTION, ENABLED)
            VALUES ('PENDING_FORM_REMINDER_DAYS', '3', 'Bekleyen formlar için bildirim gönderme süresi (gün)', 1)
            """, fetch=False)

            execute_query("""
            IF NOT EXISTS (SELECT * FROM REMINDER_SETTINGS WHERE SETTING_NAME = 'LAST_UNASSIGNED_REMINDER_SENT')
            INSERT INTO REMINDER_SETTINGS (SETTING_NAME, SETTING_VALUE, DESCRIPTION, ENABLED)
            VALUES ('LAST_UNASSIGNED_REMINDER_SENT', '1970-01-01 00:00:00', 'Son atanmamış form hatırlatıcısı gönderim zamanı', 1)
            """, fetch=False)

            print("REMINDER_SETTINGS tablosu oluşturuldu ve varsayılan ayarlar eklendi.")
        except Exception as e:
            print(f"REMINDER_SETTINGS tablosu oluşturma hatası: {e}")
        # ISG_FORMS tablosu
        execute_query("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ISG_FORMS' AND xtype='U')
        CREATE TABLE ISG_FORMS (
            ID INT IDENTITY(1,1) PRIMARY KEY,
            TYPE NVARCHAR(255),
            date DATE,
            time TIME,
            userid INT,
            username NVARCHAR(255),
            duty NVARCHAR(255),
            incident_date DATE,
            incident_time TIME,
            incident_loc NVARCHAR(MAX),
            description NVARCHAR(MAX),
            status NVARCHAR(50),
            form_status NVARCHAR(50) DEFAULT 'BEKLEMEDE',
            created_at DATETIME DEFAULT GETDATE()
        )
        """, fetch=False)

        # form_status alanını ekle (eğer yoksa)
        try:
            execute_query("""
            IF NOT EXISTS (
                SELECT * FROM sys.columns
                WHERE object_id = OBJECT_ID('ISG_FORMS') AND name = 'form_status'
            )
            ALTER TABLE ISG_FORMS ADD form_status NVARCHAR(50) DEFAULT 'BEKLEMEDE'
            """, fetch=False)

            # Mevcut kayıtlar için form_status alanını doldur
            execute_query("""
            UPDATE ISG_FORMS SET form_status = status WHERE form_status IS NULL
            """, fetch=False)
        except Exception as e:
            print(f"form_status alanı ekleme hatası: {e}")

        # ISG_FORM_ASSIGNMENTS tablosu
        execute_query("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ISG_FORM_ASSIGNMENTS' AND xtype='U')
        CREATE TABLE ISG_FORM_ASSIGNMENTS (
            ID INT IDENTITY(1,1) PRIMARY KEY,
            FORM_ID INT,
            ASSIGNED_BY INT,
            ASSIGNED_TO INT,
            ESCALATED_TO INT,
            ASSIGNMENT_DATE DATETIME,
            ASSIGNMENT_TIME DATETIME,
            STATUS NVARCHAR(50),
            ASSIGNMENT_DESCRIPTION NVARCHAR(MAX),
            ASSIGNMENT_TYPE NVARCHAR(50),
            CREATED_AT DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (FORM_ID) REFERENCES ISG_FORMS(ID)
        )
        """, fetch=False)

        # ISG_FORM_PICTURES tablosu
        execute_query("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ISG_FORM_PICTURES' AND xtype='U')
        CREATE TABLE ISG_FORM_PICTURES (
            ID INT IDENTITY(1,1) PRIMARY KEY,
            FORM_ID INT,
            FILENAME NVARCHAR(255),
            CREATED_AT DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (FORM_ID) REFERENCES ISG_FORMS(ID)
        )
        """, fetch=False)

        # ISG_FORM_ATTACHMENTS tablosu (Dosya ekleri için)
        execute_query("""
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ISG_FORM_ATTACHMENTS' AND xtype='U')
        CREATE TABLE ISG_FORM_ATTACHMENTS (
            ID INT IDENTITY(1,1) PRIMARY KEY,
            FORM_ID INT,
            ASSIGNMENT_ID INT,
            ORIGINAL_FILENAME NVARCHAR(255),
            STORED_FILENAME NVARCHAR(255),
            FILE_SIZE INT,
            FILE_TYPE NVARCHAR(100),
            UPLOADED_BY INT,
            CREATED_AT DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (FORM_ID) REFERENCES ISG_FORMS(ID),
            FOREIGN KEY (ASSIGNMENT_ID) REFERENCES ISG_FORM_ASSIGNMENTS(ID)
        )
        """, fetch=False)

        print("Tablolar başarıyla oluşturuldu.")
    except Exception as e:
        print(f"Tablo oluşturma hatası: {e}")

# Mock veritabanı bölümü - Gerçek veritabanına bağlantı kurulamazsa kullanılacak
mock_db = {
    "users": [
        {"ID": 1, "NAME": "Admin", "SURNAME": "User", "USERN": "admin", "PASS": "admin", "PERMISSION": "ADMIN", "STATUS": "active"},
        {"ID": 2, "NAME": "Test", "SURNAME": "User", "USERN": "test", "PASS": "test", "PERMISSION": "USER", "STATUS": "active"},
        {"ID": 3, "NAME": "Yetkisiz", "SURNAME": "Kullanıcı", "USERN": "yetkisiz", "PASS": "yetkisiz", "PERMISSION": "NULL", "STATUS": "active"}
    ],
    "logs": [
        {
            "ID": 1,
            "USER_ID": 1,
            "USERNAME": "admin",
            "LOG_TYPE": "LOGIN",
            "LOG_DESCRIPTION": "Kullanıcı başarıyla giriş yaptı: admin",
            "IP_ADDRESS": "127.0.0.1",
            "USER_AGENT": "Mozilla/5.0",
            "RELATED_ID": None,
            "ADDITIONAL_DATA": '{"success": true}',
            "CREATED_AT": datetime.now(),
            "user_fullname": "Admin User"
        }
    ],
    "forms": [
        {
            "ID": 1,
            "TYPE": "Kaza Bildirimi",
            "date": datetime.now(),
            "time": datetime.now(),
            "userid": 2,
            "username": "test",
            "duty": "İşçi",
            "incident_date": datetime.now(),
            "incident_time": datetime.now(),
            "incident_loc": "Test Lokasyon",
            "description": "Test açıklama",
            "status": "BEKLEMEDE",
            "user_fullname": "Test User"
        }
    ],
    "assignments": [
        {
            "ID": 1,
            "FORM_ID": 1,
            "ASSIGNED_BY": 2,
            "ASSIGNED_TO": 1,
            "ASSIGNMENT_DATE": datetime.now(),
            "ASSIGNMENT_TIME": datetime.now(),
            "STATUS": "BEKLEMEDE",
            "COMMENTS": "Form oluşturuldu ve admin kullanıcısına atandı.",
            "assigned_by_name": "Test User",
            "assigned_to_name": "Admin User",
            "escalated_to_name": None
        }
    ],
    "pictures": [],
    "form_types": [
        {"ID": 1, "NAME": "Kaza Bildirimi", "DISPLAY": "Kaza Bildirimi", "STATUS": "ENABLE"},
        {"ID": 2, "NAME": "Ramak Kala", "DISPLAY": "Ramak Kala", "STATUS": "ENABLE"},
        {"ID": 3, "NAME": "Risk Bildirimi", "DISPLAY": "Risk Bildirimi", "STATUS": "ENABLE"}
    ]
}
