import os
from dotenv import load_dotenv

# .env dos<PERSON><PERSON><PERSON><PERSON>kle (eğer varsa)
load_dotenv()

# Veritabanı bağlantı bilgileri
DB_SERVER = os.getenv('DB_SERVER', 'EESBMBLDBSC1\\SQLEXPRESS')
DB_NAME = os.getenv('DB_NAME', 'EGEFRN')
DB_USER = os.getenv('DB_USER', 'EGEFRN')
DB_PASSWORD = os.getenv('DB_PASSWORD', 'jWo$60(n=2w8')

# Uygulama ayarları
SECRET_KEY = os.getenv('SECRET_KEY', 'gizli-anahtar-buraya')
DEBUG = os.getenv('DEBUG', 'True').lower() in ('true', '1', 't')

# Admin kullanıcı ID'si (varsayılan atama için)
DEFAULT_ADMIN_ID = int(os.getenv('DEFAULT_ADMIN_ID', '1054'))  # Veritabanındaki admin kullanıcının ID'si

# E-posta ayarları
SMTP_SERVER = os.getenv('SMTP_SERVER', 'relay.bayraktar.com')
SMTP_PORT = int(os.getenv('SMTP_PORT', '25'))
SMTP_USERNAME = os.getenv('SMTP_USERNAME', '<EMAIL>')
SMTP_PASSWORD = os.getenv('SMTP_PASSWORD', '')
EMAIL_SENDER = os.getenv('EMAIL_SENDER', 'ISG Bilgilendirme Sistemi <<EMAIL>>')
SEND_EMAILS = os.getenv('SEND_EMAILS', 'True').lower() in ('true', '1', 't')  # E-posta gönderimi aktif mi?
