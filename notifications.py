"""
ISG Form Yönetim <PERSON> - <PERSON><PERSON><PERSON><PERSON>ülü
Bu modül, e-posta bildirimleri ve diğer bildirim türleri için kullanılır.

Bildirim Türleri:
1. Form durumu güncellendiğinde atanan kişiye bildirim
2. Form alanları güncellendiğinde atanan kişiye bildirim
3. Form üzerinde ilk değişiklik yapıldığında oluşturan kişiye bildirim
4. Form durumu "TAMAMLANDI" olduğunda oluşturan kişiye bildirim
5. Yeni kullanıcı oluşturulduğunda kullanıcıya bilgilendirme bildirimi
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from config import SMTP_SERVER, SMTP_PORT, SMTP_USERNAME, SMTP_PASSWORD, EMAIL_SENDER, SEND_EMAILS

def send_email(to_email, subject, message):
    """E-posta gönderir"""
    if not SEND_EMAILS:
        print(f"E-posta gönderimi devre dışı. Alıcı: {to_email}, Konu: {subject}")
        return False

    try:
        # E-posta mesajını oluştur
        msg = MIMEMultipart()
        msg['From'] = EMAIL_SENDER
        msg['To'] = to_email
        msg['Subject'] = subject

        # HTML içeriği ekle
        msg.attach(MIMEText(message, 'html'))

        # SMTP sunucusuna bağlan
        server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
        #server.starttls()  # TLS güvenliği etkinleştir
        #server.login(SMTP_USERNAME, SMTP_PASSWORD)

        # E-postayı gönder
        server.send_message(msg)
        server.quit()

        print(f"E-posta başarıyla gönderildi: {to_email}")
        return True
    except Exception as e:
        print(f"E-posta gönderme hatası: {e}")
        return False

def send_form_update_notification(form_id, form_type, new_status, assigned_user, current_user, comments=None):
    """Form durumu güncellendiğinde atanan kişiye bildirim gönderir"""
    try:
        # E-posta adresi kontrolü
        if not assigned_user or 'EMAIL' not in assigned_user or not assigned_user['EMAIL']:
            print(f"Atanan kullanıcının e-posta adresi bulunamadı: {assigned_user['ID'] if assigned_user else 'Bilinmiyor'}")
            return False

        to_email = assigned_user['EMAIL']

        # E-posta konusu
        subject = f"ISG Form Durumu Güncellendi: #{form_id} - {new_status}"

        # E-posta içeriği
        message = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #007bff; color: white; padding: 10px; text-align: center; }}
                .content {{ padding: 20px; border: 1px solid #ddd; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                .status {{ font-weight: bold; }}
                .status-BEKLEMEDE {{ color: #ffc107; }}
                .status-INCELENIYOR {{ color: #17a2b8; }}
                .status-COZULDU {{ color: #28a745; }}
                .status-TAMAMLANDI {{ color: #28a745; }}
                .status-REDDEDILDI {{ color: #dc3545; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>ISG Form Sistemi - Durum Güncellemesi</h2>
                </div>
                <div class="content">
                    <p>Sayın <strong>{assigned_user['NAME']} {assigned_user['SURNAME']}</strong>,</p>

                    <p>Size atanan bir ISG formunun durumu güncellendi:</p>

                    <ul>
                        <li><strong>Form ID:</strong> {form_id}</li>
                        <li><strong>Form Tipi:</strong> {form_type}</li>
                        <li><strong>Yeni Durum:</strong> <span class="status status-{new_status}">{new_status}</span></li>
                        <li><strong>Güncelleyen:</strong> {current_user['NAME']} {current_user['SURNAME']}</li>
                        <li><strong>Tarih:</strong> {datetime.now().strftime('%d.%m.%Y %H:%M')}</li>
                    </ul>

                    <p><strong>Açıklama:</strong> {comments if comments else "Açıklama girilmedi."}</p>

                    <p>Form detaylarını görüntülemek için <a href="http://isg.bayraktar.com:4000/forms/{form_id}">tıklayınız</a>.</p>

                    <p>Saygılarımızla,<br>ISG Form Sistemi</p>
                </div>
                <div class="footer">
                    <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayınız.</p>
                </div>
            </div>
        </body>
        </html>
        """

        # E-postayı gönder
        return send_email(to_email, subject, message)
    except Exception as e:
        print(f"Form güncelleme bildirimi gönderme hatası: {e}")
        return False

def send_form_completion_notification(form_id, form_type, new_status, creator_user, current_user, comments=None):
    """Form tamamlandığında form oluşturan kişiye bildirim gönderir"""
    try:
        # E-posta adresi kontrolü
        if not creator_user or 'EMAIL' not in creator_user or not creator_user['EMAIL']:
            print(f"Form oluşturan kullanıcının e-posta adresi bulunamadı: {creator_user['ID'] if creator_user else 'Bilinmiyor'}")
            return False

        to_email = creator_user['EMAIL']

        # E-posta konusu
        subject = f"ISG Formunuz Tamamlandı: #{form_id} - {new_status}"

        # E-posta içeriği
        message = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #28a745; color: white; padding: 10px; text-align: center; }}
                .content {{ padding: 20px; border: 1px solid #ddd; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                .status {{ font-weight: bold; }}
                .status-COZULDU {{ color: #28a745; }}
                .status-TAMAMLANDI {{ color: #28a745; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>ISG Form Sistemi - Form Tamamlandı</h2>
                </div>
                <div class="content">
                    <p>Sayın <strong>{creator_user['NAME']} {creator_user['SURNAME']}</strong>,</p>

                    <p>Oluşturduğunuz ISG formunun durumu <span class="status status-{new_status}">{new_status}</span> olarak güncellendi:</p>

                    <ul>
                        <li><strong>Form ID:</strong> {form_id}</li>
                        <li><strong>Form Tipi:</strong> {form_type}</li>
                        <li><strong>Yeni Durum:</strong> <span class="status status-{new_status}">{new_status}</span></li>
                        <li><strong>Güncelleyen:</strong> {current_user['NAME']} {current_user['SURNAME']}</li>
                        <li><strong>Tarih:</strong> {datetime.now().strftime('%d.%m.%Y %H:%M')}</li>
                    </ul>

                    <p><strong>Açıklama:</strong> {comments if comments else "Açıklama girilmedi."}</p>

                    <p>Form detaylarını görüntülemek için <a href="http://isg.bayraktar.com:4000/forms/{form_id}">tıklayınız</a>.</p>

                    <p>Saygılarımızla,<br>ISG Form Sistemi</p>
                </div>
                <div class="footer">
                    <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayınız.</p>
                </div>
            </div>
        </body>
        </html>
        """

        # E-postayı gönder
        return send_email(to_email, subject, message)
    except Exception as e:
        print(f"Form tamamlama bildirimi gönderme hatası: {e}")
        return False

def send_field_update_notification(form_id, form_type, field_name, old_value, new_value, assigned_user, current_user):
    """Form alanları güncellendiğinde atanan kişiye bildirim gönderir"""
    try:
        # E-posta adresi kontrolü
        if not assigned_user or 'EMAIL' not in assigned_user or not assigned_user['EMAIL']:
            print(f"Atanan kullanıcının e-posta adresi bulunamadı: {assigned_user['ID'] if assigned_user else 'Bilinmiyor'}")
            return False

        to_email = assigned_user['EMAIL']

        # E-posta konusu
        subject = f"ISG Form Alanı Güncellendi: #{form_id} - {field_name}"

        # E-posta içeriği
        message = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #17a2b8; color: white; padding: 10px; text-align: center; }}
                .content {{ padding: 20px; border: 1px solid #ddd; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                .field-update {{ background-color: #f8f9fa; padding: 10px; border-left: 4px solid #17a2b8; margin: 10px 0; }}
                .old-value {{ color: #dc3545; text-decoration: line-through; }}
                .new-value {{ color: #28a745; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>ISG Form Sistemi - Alan Güncellemesi</h2>
                </div>
                <div class="content">
                    <p>Sayın <strong>{assigned_user['NAME']} {assigned_user['SURNAME']}</strong>,</p>

                    <p>Size atanan bir ISG formunda alan güncellemesi yapıldı:</p>

                    <ul>
                        <li><strong>Form ID:</strong> {form_id}</li>
                        <li><strong>Form Tipi:</strong> {form_type}</li>
                        <li><strong>Güncelleyen:</strong> {current_user['NAME']} {current_user['SURNAME']}</li>
                        <li><strong>Tarih:</strong> {datetime.now().strftime('%d.%m.%Y %H:%M')}</li>
                    </ul>

                    <div class="field-update">
                        <p><strong>Güncellenen Alan:</strong> {field_name}</p>
                        <p><strong>Eski Değer:</strong> <span class="old-value">{old_value if old_value else "Boş"}</span></p>
                        <p><strong>Yeni Değer:</strong> <span class="new-value">{new_value if new_value else "Boş"}</span></p>
                    </div>

                    <p>Form detaylarını görüntülemek için <a href="http://isg.bayraktar.com:4000/forms/{form_id}">tıklayınız</a>.</p>

                    <p>Saygılarımızla,<br>ISG Form Sistemi</p>
                </div>
                <div class="footer">
                    <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayınız.</p>
                </div>
            </div>
        </body>
        </html>
        """

        # E-postayı gönder
        return send_email(to_email, subject, message)
    except Exception as e:
        print(f"Alan güncelleme bildirimi gönderme hatası: {e}")
        return False

def send_first_update_notification(form_id, form_type, update_type, creator_user, current_user, details=None):
    """Form üzerinde ilk değişiklik yapıldığında oluşturan kişiye bildirim gönderir"""
    try:
        # E-posta adresi kontrolü
        if not creator_user or 'EMAIL' not in creator_user or not creator_user['EMAIL']:
            print(f"Form oluşturan kullanıcının e-posta adresi bulunamadı: {creator_user['ID'] if creator_user else 'Bilinmiyor'}")
            return False

        to_email = creator_user['EMAIL']

        # E-posta konusu
        subject = f"ISG Formunuzda İlk Güncelleme: #{form_id}"

        # E-posta içeriği
        message = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #007bff; color: white; padding: 10px; text-align: center; }}
                .content {{ padding: 20px; border: 1px solid #ddd; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                .update-info {{ background-color: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; margin: 10px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>ISG Form Sistemi - İlk Güncelleme</h2>
                </div>
                <div class="content">
                    <p>Sayın <strong>{creator_user['NAME']} {creator_user['SURNAME']}</strong>,</p>

                    <p>Oluşturduğunuz ISG formunda ilk güncelleme yapıldı:</p>

                    <ul>
                        <li><strong>Form ID:</strong> {form_id}</li>
                        <li><strong>Form Tipi:</strong> {form_type}</li>
                        <li><strong>Güncelleme Türü:</strong> {update_type}</li>
                        <li><strong>Güncelleyen:</strong> {current_user['NAME']} {current_user['SURNAME']}</li>
                        <li><strong>Tarih:</strong> {datetime.now().strftime('%d.%m.%Y %H:%M')}</li>
                    </ul>

                    {f'''
                    <div class="update-info">
                        <p><strong>Güncelleme Detayları:</strong></p>
                        <p>{details}</p>
                    </div>
                    ''' if details else ''}

                    <p>Form detaylarını görüntülemek için <a href="http://isg.bayraktar.com:4000/forms/{form_id}">tıklayınız</a>.</p>

                    <p>Saygılarımızla,<br>ISG Form Sistemi</p>
                </div>
                <div class="footer">
                    <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayınız.</p>
                </div>
            </div>
        </body>
        </html>
        """

        # E-postayı gönder
        return send_email(to_email, subject, message)
    except Exception as e:
        print(f"İlk güncelleme bildirimi gönderme hatası: {e}")
        return False

def send_new_user_notification(user_id, name, surname, username, email, permission, password=None):
    """Yeni kullanıcı oluşturulduğunda kullanıcıya bilgilendirme e-postası gönderir"""
    try:
        # E-posta adresi kontrolü
        if not email:
            print(f"Kullanıcının e-posta adresi bulunamadı: {user_id}")
            return False

        to_email = email

        # E-posta konusu
        subject = "ISG Form Sistemi - Yeni Kullanıcı Hesabınız Oluşturuldu"

        # Yetki türünü Türkçe olarak belirle
        permission_text = "Yönetici" if permission == "ADMIN" else "Kullanıcı" if permission == "USER" else "Yetkisiz"

        # E-posta içeriği
        message = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #28a745; color: white; padding: 10px; text-align: center; }}
                .content {{ padding: 20px; border: 1px solid #ddd; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                .account-info {{ background-color: #f8f9fa; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0; }}
                .important {{ color: #dc3545; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>ISG Form Sistemi - Hoş Geldiniz</h2>
                </div>
                <div class="content">
                    <p>Sayın <strong>{name} {surname}</strong>,</p>

                    <p>ISG Form Yönetim Sistemi'nde sizin için yeni bir hesap oluşturulmuştur. Hesap bilgileriniz aşağıdadır:</p>

                    <div class="account-info">
                        <p><strong>Kullanıcı ID:</strong> {user_id}</p>
                        <p><strong>Kullanıcı Adı:</strong> {username}</p>
                        <p><strong>E-posta:</strong> {email}</p>
                        <p><strong>Yetki Seviyesi:</strong> {permission_text}</p>
                        {f'<p><strong>Şifre:</strong> <span class="important">{password}</span></p>' if password else ''}
                    </div>

                    {f'''
                    <p class="important">Önemli: Lütfen ilk girişinizde şifrenizi değiştirmeyi unutmayınız!</p>
                    ''' if password else ''}

                    <p>Sisteme giriş yapmak için <a href="http://isg.bayraktar.com:4000/login">tıklayınız</a>.</p>

                    <p>Saygılarımızla,<br>ISG Form Sistemi</p>
                </div>
                <div class="footer">
                    <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayınız.</p>
                </div>
            </div>
        </body>
        </html>
        """

        # E-postayı gönder
        return send_email(to_email, subject, message)
    except Exception as e:
        print(f"Yeni kullanıcı bildirimi gönderme hatası: {e}")
        return False
