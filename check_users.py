import db

def main():
    try:
        # <PERSON><PERSON><PERSON><PERSON> veritabanı bağlantısı kullanarak sorgu yap
        conn = db.get_db_connection()
        if conn:
            cursor = conn.cursor()
            cursor.execute("SELECT ID, NAME, SURNAME, USERN, PERMISSION, STATUS FROM USERS")
            
            print("Kullanıcı Durumları:")
            for row in cursor.fetchall():
                print(f"ID: {row[0]}, Ad: {row[1]} {row[2]}, Kullanıcı Adı: {row[3]}, Yetki: {row[4]}, Durum: \"{row[5]}\"")
            
            conn.close()
    except Exception as e:
        print(f"Hata: {e}")

if __name__ == "__main__":
    main()
