<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ISG Form Yönetim Sistemi{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Corporate ERP Theme CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/corporate.css') }}">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-shield-check me-2"></i>ISG Form Sistemi
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                {% if session.user_id %}
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.path == url_for('dashboard') or request.path == url_for('logs') %}active{% endif %}"
                           href="#" id="controlDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-speedometer2 me-1"></i> Yönetim
                        </a>
                        <ul class="dropdown-menu shadow" aria-labelledby="controlDropdown">
                            <li>
                                <a class="dropdown-item {% if request.path == url_for('dashboard') %}active{% endif %}" href="{{ url_for('dashboard') }}">
                                    <i class="bi bi-speedometer2 me-2"></i>Kontrol Paneli
                                </a>
                            </li>
                            {% if session.permission == 'ADMIN     ' or session.permission == 'ADMIN' %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item {% if request.path == url_for('logs') %}active{% endif %}" href="{{ url_for('logs') }}">
                                    <i class="bi bi-list-ul me-2"></i>Sistem Logları
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {% if request.path == url_for('reminder_settings') %}active{% endif %}" href="{{ url_for('reminder_settings') }}">
                                    <i class="bi bi-bell me-2"></i>Hatırlatıcı Ayarları
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('form_list') %}active{% endif %}" href="{{ url_for('form_list') }}">
                            <i class="bi bi-list-check me-1"></i> Formlar
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('form_create') %}active{% endif %}" href="{{ url_for('form_create') }}">
                            <i class="bi bi-plus-circle me-1"></i> Yeni Form
                        </a>
                    </li>
                    {% if session.permission == 'ADMIN     ' or session.permission == 'ADMIN' %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('user_list') %}active{% endif %}" href="{{ url_for('user_list') }}">
                            <i class="bi bi-people me-1"></i> Kullanıcı Yönetimi
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i> {{ session.name }}
                            {% if session.permission == 'ADMIN     ' or session.permission == 'ADMIN' %}
                            <span class="badge bg-danger">Admin</span>
                            {% elif session.permission == 'USER' %}
                            <span class="badge bg-info">Kullanıcı</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ session.permission }}</span>
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            <li><a class="dropdown-item {% if request.path == url_for('profile') %}active{% endif %}" href="{{ url_for('profile') }}"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="bi bi-box-arrow-right me-2"></i>Çıkış Yap</a></li>
                        </ul>
                    </li>
                </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Flash Mesajları -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show shadow-sm">
                        <i class="bi bi-info-circle me-2"></i>{{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Ana İçerik -->
    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">&copy; 2025 ISG Form Yönetim Sistemi</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <p class="mb-0">BayFLOW</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
