{% extends 'base.html' %}

{% block title %}Kontrol Paneli - ISG Form Yönetim Sistemi{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="d-sm-flex align-items-center justify-content-between">
            <h1 class="mb-0">Kontrol Paneli</h1>
            <div>
                <a href="{{ url_for('form_create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>Yeni Form Oluştur
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4 equal-height-cards">
    <div class="col-md-4">
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="icon-circle bg-primary bg-opacity-10 me-3">
                        <i class="bi bi-person-circle text-primary fs-4"></i>
                    </div>
                    <div>
                        <h6 class="card-subtitle text-muted mb-1">Hoş Geldiniz</h6>
                        <h5 class="card-title mb-0">{{ session.name }}</h5>
                    </div>
                </div>
                <p class="card-text">
                    {% if is_admin %}
                    <span class="badge bg-danger">Admin Yetkileri</span>
                    {% else %}
                    <span class="badge bg-info">Kullanıcı Yetkileri</span>
                    {% endif %}
                </p>
                <p class="card-text text-muted small mt-auto">Son giriş: {{ current_time }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="icon-circle bg-success bg-opacity-10 me-3">
                        <i class="bi bi-lightning-charge text-success fs-4"></i>
                    </div>
                    <div>
                        <h6 class="card-subtitle text-muted mb-1">Hızlı Erişim</h6>
                        <h5 class="card-title mb-0">İşlemler</h5>
                    </div>
                </div>
                <div class="d-grid gap-2 mt-auto">
                    <a href="{{ url_for('form_create') }}" class="btn btn-outline-primary">
                        <i class="bi bi-plus-circle me-2"></i>Yeni Form Oluştur
                    </a>
                    <a href="{{ url_for('form_list') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-list-check me-2"></i>Tüm Formları Görüntüle
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="icon-circle bg-info bg-opacity-10 me-3">
                        <i class="bi bi-question-circle text-info fs-4"></i>
                    </div>
                    <div>
                        <h6 class="card-subtitle text-muted mb-1">Destek</h6>
                        <h5 class="card-title mb-0">Yardım</h5>
                    </div>
                </div>
                <p class="card-text">Sorun yaşarsanız lütfen bildirimde bulunun.</p>
                <div class="mt-auto">
                    <a href="mailto:<EMAIL>?subject=ISG%20Form%20Sistemi%20Yardım%20Talebi" class="btn btn-sm btn-outline-info">
                        <i class="bi bi-info-circle me-2"></i>Yardım Merkezi
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{% if is_admin %}Son Eklenen Formlar{% else %}Size Atanmış Son Formlar{% endif %}</h5>
                <span class="badge bg-primary rounded-pill">{{ forms|length if forms else 0 }}</span>
            </div>
            <div class="card-body">
                {% if forms %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Tür</th>
                                <th>Tarih</th>
                                <th>Kullanıcı</th>
                                <th>Durum</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for form in forms %}
                            <tr>
                                <td>
                                    <span class="fw-medium">{{ form.ID }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">
                                            {% if form.TYPE == 'Ramak Kala' %}
                                            <i class="bi bi-exclamation-triangle text-warning"></i>
                                            {% elif form.TYPE == 'Kaza' %}
                                            <i class="bi bi-bandaid text-danger"></i>
                                            {% elif form.TYPE == 'Öneri' %}
                                            <i class="bi bi-lightbulb text-primary"></i>
                                            {% else %}
                                            <i class="bi bi-file-text text-secondary"></i>
                                            {% endif %}
                                        </span>
                                        {{ form.TYPE }}
                                    </div>
                                </td>
                                <td>{{ form.date|format_date }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="avatar-sm bg-light text-primary me-2">
                                            {{ form.user_fullname[:1] if form.user_fullname else '?' }}
                                        </span>
                                        <span>{{ form.user_fullname if form.user_fullname else 'Bilinmiyor' }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge {% if form.status == 'BEKLEMEDE' %}bg-warning{% elif form.status == 'TAMAMLANDI' %}bg-success{% elif form.status == 'REDDEDILDI' %}bg-danger{% else %}bg-primary{% endif %}">
                                        {{ form.status }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('form_detail', form_id=form.ID) }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-eye me-1"></i>Görüntüle
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-end mt-3">
                    <a href="{{ url_for('form_list') }}" class="btn btn-outline-primary">
                        <i class="bi bi-list-ul me-2"></i>Tüm Formları Görüntüle
                    </a>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-3">Henüz form bulunmamaktadır.</p>
                    <a href="{{ url_for('form_create') }}" class="btn btn-primary mt-2">
                        <i class="bi bi-plus-circle me-2"></i>Yeni Form Oluştur
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
