{% extends 'base.html' %}

{% block title %}Hatırlatıcı Ayarları{% endblock %}

{% block extra_css %}
<style>
    .form-switch .form-check-input {
        width: 3em;
        height: 1.5em;
    }
    .card-header {
        background-color: #f8f9fa;
    }
    .setting-description {
        color: #6c757d;
        font-size: 0.9rem;
    }
    .last-updated {
        font-size: 0.8rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <h1>Hatırlatıcı Ayarları</h1>
            <p class="text-muted">Bu sayfadan otomatik hatırlatıcı ayarlarını yapılandırabilirsiniz.</p>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Atanmamış Form Hatırlatıcıları</h5>
                </div>
                <div class="card-body">
                    <p>Henüz atanmamış formlar için belirtilen e-posta adresine düzenli olarak hatırlatıcı gönderilir.</p>
                    
                    <form action="{{ url_for('update_reminder_setting') }}" method="post" class="mb-4">
                        <input type="hidden" name="setting_name" value="UNASSIGNED_FORM_REMINDER_EMAIL">
                        
                        <div class="mb-3">
                            <label for="unassigned_email" class="form-label">E-posta Adresi</label>
                            <input type="email" class="form-control" id="unassigned_email" name="setting_value" 
                                   value="{{ settings.get('UNASSIGNED_FORM_REMINDER_EMAIL', {}).get('SETTING_VALUE', '') }}" required>
                            <div class="setting-description">Atanmamış formlar için bildirim gönderilecek e-posta adresi</div>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="unassigned_email_enabled" name="enabled" 
                                   {% if settings.get('UNASSIGNED_FORM_REMINDER_EMAIL', {}).get('ENABLED', 0) == 1 %}checked{% endif %}>
                            <label class="form-check-label" for="unassigned_email_enabled">Etkinleştir</label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Kaydet</button>
                        
                        {% if settings.get('UNASSIGNED_FORM_REMINDER_EMAIL', {}).get('UPDATED_AT') %}
                        <div class="last-updated mt-2">
                            Son güncelleme: {{ settings.get('UNASSIGNED_FORM_REMINDER_EMAIL', {}).get('UPDATED_AT')|format_datetime }}
                        </div>
                        {% endif %}
                    </form>
                    
                    <hr>
                    
                    <form action="{{ url_for('update_reminder_setting') }}" method="post">
                        <input type="hidden" name="setting_name" value="UNASSIGNED_FORM_REMINDER_FREQUENCY">
                        
                        <div class="mb-3">
                            <label for="unassigned_frequency" class="form-label">Gönderim Sıklığı (Saat)</label>
                            <input type="number" class="form-control" id="unassigned_frequency" name="setting_value" 
                                   value="{{ settings.get('UNASSIGNED_FORM_REMINDER_FREQUENCY', {}).get('SETTING_VALUE', '24') }}" min="1" max="168" required>
                            <div class="setting-description">Atanmamış formlar için bildirim gönderme sıklığı (saat cinsinden)</div>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="unassigned_frequency_enabled" name="enabled" 
                                   {% if settings.get('UNASSIGNED_FORM_REMINDER_FREQUENCY', {}).get('ENABLED', 0) == 1 %}checked{% endif %}>
                            <label class="form-check-label" for="unassigned_frequency_enabled">Etkinleştir</label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Kaydet</button>
                        
                        {% if settings.get('UNASSIGNED_FORM_REMINDER_FREQUENCY', {}).get('UPDATED_AT') %}
                        <div class="last-updated mt-2">
                            Son güncelleme: {{ settings.get('UNASSIGNED_FORM_REMINDER_FREQUENCY', {}).get('UPDATED_AT')|format_datetime }}
                        </div>
                        {% endif %}
                    </form>
                    
                    {% if settings.get('LAST_UNASSIGNED_REMINDER_SENT', {}).get('SETTING_VALUE') %}
                    <div class="mt-3 p-2 bg-light rounded">
                        <small>Son gönderim: {{ settings.get('LAST_UNASSIGNED_REMINDER_SENT', {}).get('SETTING_VALUE') }}</small>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Bekleyen Form Hatırlatıcıları</h5>
                </div>
                <div class="card-body">
                    <p>Belirli bir süredir bekleyen formlar için atanan kişilere hatırlatıcı gönderilir.</p>
                    
                    <form action="{{ url_for('update_reminder_setting') }}" method="post">
                        <input type="hidden" name="setting_name" value="PENDING_FORM_REMINDER_DAYS">
                        
                        <div class="mb-3">
                            <label for="pending_days" class="form-label">Bekleme Süresi (Gün)</label>
                            <input type="number" class="form-control" id="pending_days" name="setting_value" 
                                   value="{{ settings.get('PENDING_FORM_REMINDER_DAYS', {}).get('SETTING_VALUE', '3') }}" min="1" max="30" required>
                            <div class="setting-description">Kaç gün bekleyen formlar için hatırlatıcı gönderileceği</div>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="pending_days_enabled" name="enabled" 
                                   {% if settings.get('PENDING_FORM_REMINDER_DAYS', {}).get('ENABLED', 0) == 1 %}checked{% endif %}>
                            <label class="form-check-label" for="pending_days_enabled">Etkinleştir</label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Kaydet</button>
                        
                        {% if settings.get('PENDING_FORM_REMINDER_DAYS', {}).get('UPDATED_AT') %}
                        <div class="last-updated mt-2">
                            Son güncelleme: {{ settings.get('PENDING_FORM_REMINDER_DAYS', {}).get('UPDATED_AT')|format_datetime }}
                        </div>
                        {% endif %}
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Hatırlatıcıları Manuel Çalıştır</h5>
                </div>
                <div class="card-body">
                    <p>Hatırlatıcıları hemen çalıştırmak için aşağıdaki butonları kullanabilirsiniz.</p>
                    
                    <form action="{{ url_for('run_reminder_now') }}" method="post" class="d-inline">
                        <input type="hidden" name="reminder_type" value="unassigned">
                        <button type="submit" class="btn btn-warning me-2">Atanmamış Form Hatırlatıcısını Çalıştır</button>
                    </form>
                    
                    <form action="{{ url_for('run_reminder_now') }}" method="post" class="d-inline">
                        <input type="hidden" name="reminder_type" value="pending">
                        <button type="submit" class="btn btn-danger">Bekleyen Form Hatırlatıcısını Çalıştır</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Form gönderiminde onay iste
    document.addEventListener('DOMContentLoaded', function() {
        const manualForms = document.querySelectorAll('form[action="{{ url_for("run_reminder_now") }}"]');
        
        manualForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const reminderType = this.querySelector('input[name="reminder_type"]').value;
                const reminderName = reminderType === 'unassigned' ? 'Atanmamış Form' : 'Bekleyen Form';
                
                if (confirm(`${reminderName} hatırlatıcısını şimdi çalıştırmak istediğinize emin misiniz?`)) {
                    this.submit();
                }
            });
        });
    });
</script>
{% endblock %}
