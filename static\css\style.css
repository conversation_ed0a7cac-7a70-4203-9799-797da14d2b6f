/* ISG Form Yö<PERSON>im <PERSON> CSS */

/* <PERSON> */
.icon-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

/* Avatar stilleri */
.avatar-sm {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Form durumlarına göre renklendirme */
.form-status-BEKLEMEDE {
    background-color: #fff3cd;
}

.form-status-inceleniyor {
    background-color: #cfe2ff;
}

.form-status-cozuldu {
    background-color: #d1e7dd;
}

.form-status-tamamlandi {
    background-color: #d1e7dd;
}

.form-status-reddedildi {
    background-color: #f8d7da;
}

/* Timeline stillemesi */
.timeline {
    position: relative;
    max-height: 400px;
    overflow-y: auto;
}

.timeline:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #ddd;
    left: 20px;
    margin-left: -1px;
}

.timeline .card {
    position: relative;
    margin-left: 40px;
}

.timeline .card:before {
    content: '';
    position: absolute;
    top: 15px;
    left: -35px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #007bff;
    border: 2px solid #fff;
}

/* Tablo kolon genişlikleri */
#formsTable th, #formsTable td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#formsTable th:nth-child(1), #formsTable td:nth-child(1) { /* ID */
    width: 5%;
    min-width: 50px;
}

#formsTable th:nth-child(2), #formsTable td:nth-child(2) { /* Tür */
    width: 15%;
    min-width: 120px;
}

#formsTable th:nth-child(3), #formsTable td:nth-child(3) { /* Tarih */
    width: 10%;
    min-width: 90px;
}

#formsTable th:nth-child(4), #formsTable td:nth-child(4) { /* Kullanıcı */
    width: 12%;
    min-width: 120px;
}

#formsTable th:nth-child(5), #formsTable td:nth-child(5) { /* Olay Yeri */
    width: 10%;
    max-width: 75px;
}

#formsTable th:nth-child(6), #formsTable td:nth-child(6) { /* Durum */
    width: 10%;
    max-width: 75px;
}

#formsTable th:nth-child(7), #formsTable td:nth-child(7) { /* Atanan Kişi */
    width: 13%;
    min-width: 120px;
}

#formsTable th:nth-child(8), #formsTable td:nth-child(8) { /* İşlemler */
    width: 10%;
    min-width: 100px;
}

/* Dosya simgeleri stilleri */
.file-item {
    transition: all 0.2s ease;
    padding: 10px;
    border-radius: 5px;
}

.file-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.file-item a {
    text-decoration: none;
    color: inherit;
    display: block;
}

.file-name {
    max-width: 100%;
    font-size: 0.8rem;
    margin-top: 5px;
    color: #495057;
}

/* Responsive ayarlamalar */
@media (max-width: 768px) {
    .timeline .card {
        margin-left: 30px;
    }

    .timeline .card:before {
        left: -25px;
    }

    /* Mobil görünümde tablo kaydırma */
    .table-responsive {
        overflow-x: auto;
    }
}
