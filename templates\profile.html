{% extends 'base.html' %}

{% block title %}Profil - ISG Form Yönetim Sistemi{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="mb-1">Profil</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Kontrol Paneli</a></li>
                        <li class="breadcrumb-item active">Profil</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Kullanıcı Bilgileri</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Kullanıcı ID:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ user.ID }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Ad Soyad:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ user.NAME }} {{ user.SURNAME }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Kullanıcı Adı:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ user.USERN }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>E-posta:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ user.EMAIL if user.EMAIL else 'Belirtilmemiş' }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Yetki:</strong>
                    </div>
                    <div class="col-md-8">
                        <span class="badge
                            {% if user.PERMISSION and 'ADMIN' in user.PERMISSION %}bg-danger
                            {% elif user.PERMISSION and 'USER' in user.PERMISSION %}bg-info
                            {% elif user.PERMISSION and 'NULL' in user.PERMISSION %}bg-secondary
                            {% else %}bg-secondary{% endif %}">
                            {% if user.PERMISSION and 'ADMIN' in user.PERMISSION %}ADMIN
                            {% elif user.PERMISSION and 'USER' in user.PERMISSION %}USER
                            {% elif user.PERMISSION and 'NULL' in user.PERMISSION %}YETKİSİZ
                            {% elif not user.PERMISSION %}YETKİSİZ
                            {% else %}{{ user.PERMISSION.strip() }}{% endif %}
                        </span>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Durum:</strong>
                    </div>
                    <div class="col-md-8">
                        <span class="badge {% if user.STATUS and 'active' in user.STATUS %}bg-success{% else %}bg-secondary{% endif %}">
                            {{ 'Aktif' if user.STATUS and 'active' in user.STATUS else 'Pasif' }}
                        </span>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Oluşturulma Tarihi:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ user.CREATED_DATE if user.CREATED_DATE else 'Belirtilmemiş' }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>Son Giriş Tarihi:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ user.LAST_LOGIN_DATE if user.LAST_LOGIN_DATE else 'Belirtilmemiş' }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Şifre Değiştir</h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('update_password') }}" method="post">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Mevcut Şifre</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">Yeni Şifre</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                        <div class="form-text">Şifreniz en az 8 karakter uzunluğunda olmalıdır.</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Yeni Şifre (Tekrar)</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Şifreyi Güncelle</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Şifre doğrulama kontrolü
        const form = document.querySelector('form');
        const newPasswordInput = document.getElementById('new_password');
        const confirmPasswordInput = document.getElementById('confirm_password');

        form.addEventListener('submit', function(event) {
            // Şifre uzunluğu kontrolü
            if (newPasswordInput.value.length < 8) {
                event.preventDefault();
                alert('Şifreniz en az 8 karakter uzunluğunda olmalıdır.');
                return;
            }

            // Şifre eşleşme kontrolü
            if (newPasswordInput.value !== confirmPasswordInput.value) {
                event.preventDefault();
                alert('Yeni şifre ve tekrarı eşleşmiyor.');
                return;
            }
        });
    });
</script>
{% endblock %}
