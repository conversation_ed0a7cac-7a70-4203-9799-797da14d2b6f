{% extends 'base.html' %}

{% block title %}Kullanıcı Yönetimi - ISG Form Yönetim Sistemi{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="mb-1">Kullanıcı Yönetimi</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Kontrol Paneli</a></li>
                        <li class="breadcrumb-item active">Kullanıcı Yönetimi</li>
                    </ol>
                </nav>
            </div>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                    <i class="bi bi-person-plus me-2"></i><PERSON><PERSON>
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6 mb-2 mb-md-0">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-people me-2 text-primary"></i>
                            <h5 class="mb-0">Kullanıcı Listesi</h5>
                            <span class="badge bg-primary rounded-pill ms-2">{{ users|length if users else 0 }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-search text-muted"></i>
                            </span>
                            <input type="text" id="searchInput" class="form-control border-start-0" placeholder="Ara...">
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if users %}
                <div class="table-responsive">
                    <table class="table table-hover" id="usersTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Ad Soyad</th>
                                <th>Kullanıcı Adı</th>
                                <th>Yetki</th>
                                <th>Durum</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>{{ user.ID }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="avatar-sm bg-light text-primary me-2">
                                            {{ user.NAME[:1] if user.NAME else '?' }}
                                        </span>
                                        <span>{{ user.NAME }} {{ user.SURNAME }}</span>
                                    </div>
                                </td>
                                <td>{{ user.USERN }}</td>
                                <td>
                                    <span class="badge
                                        {% if user.PERMISSION and 'ADMIN' in user.PERMISSION %}bg-danger
                                        {% elif user.PERMISSION and 'User' in user.PERMISSION %}bg-info
                                        {% elif user.PERMISSION and 'NULL' in user.PERMISSION %}bg-secondary
                                        {% else %}bg-secondary{% endif %}">
                                        {% if user.PERMISSION and 'ADMIN' in user.PERMISSION %}ADMIN
                                        {% elif user.PERMISSION and 'USER' in user.PERMISSION %}USER
                                        {% elif user.PERMISSION and 'NULL' in user.PERMISSION %}YETKİSİZ
                                        {% elif not user.PERMISSION %}YETKİSİZ
                                        {% else %}{{ user.PERMISSION.strip() }}{% endif %}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge {% if user.STATUS and 'active' in user.STATUS %}bg-success{% else %}bg-secondary{% endif %}">
                                        {{ 'Aktif' if user.STATUS and 'active' in user.STATUS else 'Pasif' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                            İşlemler
                                        </button>
                                        <ul class="dropdown-menu">
                                            {% if user.STATUS and 'active' in user.STATUS %}
                                            <li>
                                                <button type="button" class="dropdown-item text-warning"
                                                        data-bs-toggle="modal" data-bs-target="#statusModal"
                                                        data-user-id="{{ user.ID }}"
                                                        data-user-name="{{ user.NAME }} {{ user.SURNAME }}"
                                                        data-status="disabled">
                                                    <i class="bi bi-slash-circle me-2"></i>Devre Dışı Bırak
                                                </button>
                                            </li>
                                            {% else %}
                                            <li>
                                                <button type="button" class="dropdown-item text-success"
                                                        data-bs-toggle="modal" data-bs-target="#statusModal"
                                                        data-user-id="{{ user.ID }}"
                                                        data-user-name="{{ user.NAME }} {{ user.SURNAME }}"
                                                        data-status="active">
                                                    <i class="bi bi-check-circle me-2"></i>Etkinleştir
                                                </button>
                                            </li>
                                            {% endif %}
                                            <li>
                                                <button type="button" class="dropdown-item text-primary"
                                                        data-bs-toggle="modal" data-bs-target="#passwordModal"
                                                        data-user-id="{{ user.ID }}"
                                                        data-user-name="{{ user.NAME }} {{ user.SURNAME }}">
                                                    <i class="bi bi-key me-2"></i>Şifre Sıfırla
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-3">Henüz kullanıcı bulunmamaktadır.</p>
                    <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#createUserModal">
                        <i class="bi bi-person-plus me-2"></i>Yeni Kullanıcı Oluştur
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Yeni Kullanıcı Oluşturma Modal -->
<div class="modal fade" id="createUserModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Yeni Kullanıcı Oluştur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('user_create') }}" method="post">
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Ad</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="surname" class="form-label">Soyad</label>
                            <input type="text" class="form-control" id="surname" name="surname" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="username" class="form-label">Kullanıcı Adı</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">E-posta</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Şifre</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="permission" class="form-label">Yetki</label>
                        <select class="form-select" id="permission" name="permission" required>
                            <option value="User">Kullanıcı</option>
                            <option value="ADMIN">Admin</option>
                            <option value="NULL">Yetkisiz (Giriş Yapamaz)</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Kullanıcı Oluştur</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Kullanıcı Durumu Değiştirme Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Kullanıcı Durumunu Değiştir</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="statusForm" action="" method="post">
                <div class="modal-body">
                    <input type="hidden" id="statusInput" name="status" value="">
                    <p id="statusMessage"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Onayla</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Şifre Sıfırlama Modal -->
<div class="modal fade" id="passwordModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Şifre Sıfırla</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="passwordForm" action="" method="post">
                <div class="modal-body">
                    <p id="passwordUserName"></p>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">Yeni Şifre</label>
                        <input type="password" class="form-control" id="newPassword" name="password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Şifreyi Güncelle</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Kullanıcı arama işlevi
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('keyup', function() {
                const searchText = searchInput.value.toLowerCase();
                const usersTable = document.getElementById('usersTable');

                if (usersTable) {
                    const tbody = usersTable.querySelector('tbody');
                    if (tbody) {
                        const rows = tbody.getElementsByTagName('tr');
                        for (let i = 0; i < rows.length; i++) {
                            const rowText = rows[i].textContent.toLowerCase();
                            if (rowText.includes(searchText)) {
                                rows[i].style.display = '';
                            } else {
                                rows[i].style.display = 'none';
                            }
                        }
                    }
                }
            });
        }

        // Durum değiştirme modalı
        const statusModal = document.getElementById('statusModal');
        if (statusModal) {
            statusModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const userId = button.getAttribute('data-user-id');
                const userName = button.getAttribute('data-user-name');
                const status = button.getAttribute('data-status');

                const statusForm = document.getElementById('statusForm');
                const statusInput = document.getElementById('statusInput');
                const statusMessage = document.getElementById('statusMessage');

                statusForm.action = "{{ url_for('user_list') }}/" + userId + "/status";
                statusInput.value = status;

                if (status === 'active') {
                    statusMessage.innerHTML = `<strong>${userName}</strong> kullanıcısını etkinleştirmek istediğinize emin misiniz?`;
                } else {
                    statusMessage.innerHTML = `<strong>${userName}</strong> kullanıcısını devre dışı bırakmak istediğinize emin misiniz?`;
                }
            });
        }

        // Şifre sıfırlama modalı
        const passwordModal = document.getElementById('passwordModal');
        if (passwordModal) {
            passwordModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const userId = button.getAttribute('data-user-id');
                const userName = button.getAttribute('data-user-name');

                const passwordForm = document.getElementById('passwordForm');
                const passwordUserName = document.getElementById('passwordUserName');

                passwordForm.action = "{{ url_for('user_list') }}/" + userId + "/password";
                passwordUserName.innerHTML = `<strong>${userName}</strong> kullanıcısı için yeni şifre belirleyin:`;
            });
        }
    });
</script>
{% endblock %}
