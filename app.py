from flask import Flask, render_template, request, redirect, url_for, flash, session, send_from_directory, jsonify
from werkzeug.security import check_password_hash, generate_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime
import db
import logger
import notifications
import reminders
from config import SECRET_KEY, DEBUG, DEFAULT_ADMIN_ID
import sys
import os
import uuid

app = Flask(__name__)
app.secret_key = SECRET_KEY
app.config['DEBUG'] = DEBUG

# Dosya yükleme ayarları
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16 MB maksimum dosya boyutu
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'txt'}

# Veritabanı bağlantısını test et, başarısız olursa mock veritabanı kullanıldığını bildir
try:
    conn = db.get_db_connection()
    if conn:
        print("Veritabanı bağlantısı başarılı. Gerçek veritabanı kullanılıyor.")
        # Tabloları oluştur
        db.create_tables()
        # Log tablosunu oluştur
        logger.create_log_table()
        conn.close()
    else:
        print("Veritabanı bağlantısı kurulamadı. Mock veritabanı kullanılacak.")
        # Mock veritabanı fonksiyonlarını aktif et
        def override_db_functions():
            for func_name in ['get_user_by_username', 'get_user_by_id', 'get_all_users',
                             'get_all_users_for_management', 'update_user_status', 'update_user_password',
                             'create_user', 'get_form_types', 'get_all_forms', 'get_form_by_id',
                             'get_form_assignments', 'create_form', 'update_form_status',
                             'get_form_pictures', 'get_db_connection', 'execute_query']:
                # Asıl fonksiyonun adını değiştir
                real_func = getattr(db, func_name)
                setattr(db, f"real_{func_name}", real_func)

                # Fonksiyonu oluştur
                def create_mock_function(f_name):
                    def mock_function(*args, **kwargs):
                        print(f"Mock {f_name} çağrıldı")
                        if f_name == 'get_user_by_username':
                            for user in db.mock_db["users"]:
                                if user["USERN"] == args[0]:
                                    return user
                            return None
                        elif f_name == 'get_user_by_id':
                            for user in db.mock_db["users"]:
                                if user["ID"] == args[0]:
                                    return user
                            return None
                        elif f_name == 'get_all_users':
                            # USER ve ADMIN yetkisine sahip aktif kullanıcıları getir
                            return [u for u in db.mock_db["users"] if u.get("PERMISSION") in ["USER", "ADMIN"] and u.get("STATUS") == "active"]
                        elif f_name == 'get_all_users_for_management':
                            # Tüm kullanıcıları yönetim için getir
                            return db.mock_db["users"]
                        elif f_name == 'update_user_status':
                            user_id, new_status = args
                            for user in db.mock_db["users"]:
                                if user["ID"] == user_id:
                                    user["STATUS"] = new_status
                                    return True
                            return False
                        elif f_name == 'update_user_password':
                            user_id, new_password = args
                            for user in db.mock_db["users"]:
                                if user["ID"] == user_id:
                                    user["PASS"] = new_password
                                    return True
                            return False
                        elif f_name == 'create_user':
                            name, surname, username, password, email, permission = args
                            new_id = max([u["ID"] for u in db.mock_db["users"]]) + 1
                            new_user = {
                                "ID": new_id,
                                "NAME": name,
                                "SURNAME": surname,
                                "USERN": username,
                                "PASS": password,
                                "EMAIL": email,
                                "PERMISSION": permission,
                                "STATUS": "active",
                                "LAST_LOGIN_DATE": None
                            }
                            db.mock_db["users"].append(new_user)
                            return new_id
                        elif f_name == 'get_form_types':
                            return db.mock_db["form_types"]
                        elif f_name == 'get_all_forms':
                            limit = args[0] if args else 100
                            return db.mock_db["forms"][:limit]
                        elif f_name == 'get_form_by_id':
                            for form in db.mock_db["forms"]:
                                if form["ID"] == args[0]:
                                    return form
                            return None
                        elif f_name == 'get_form_assignments':
                            return [a for a in db.mock_db["assignments"] if a["FORM_ID"] == args[0]]
                        elif f_name == 'create_form':
                            form_data, user_id, admin_id = args
                            form_id = len(db.mock_db["forms"]) + 1

                            user = None
                            for u in db.mock_db["users"]:
                                if u["ID"] == user_id:
                                    user = u
                                    break

                            username = user["USERN"] if user else "unknown"

                            new_form = {
                                "ID": form_id,
                                "TYPE": form_data['type'],
                                "date": datetime.now(),
                                "time": datetime.now(),
                                "userid": user_id,
                                "username": username,
                                "duty": form_data['duty'],
                                "incident_date": datetime.strptime(form_data['incident_date'], "%Y-%m-%d") if form_data['incident_date'] else None,
                                "incident_time": datetime.strptime(form_data['incident_time'], "%H:%M") if form_data['incident_time'] else None,
                                "incident_loc": form_data['incident_loc'],
                                "description": form_data['description'],
                                "status": "",
                                "form_status": "BEKLEMEDE",
                                "user_fullname": f"{user['NAME']} {user['SURNAME']}" if user else "Unknown User"
                            }

                            db.mock_db["forms"].append(new_form)

                            new_assignment = {
                                "ID": len(db.mock_db["assignments"]) + 1,
                                "FORM_ID": form_id,
                                "ASSIGNED_BY": user_id,
                                "ASSIGNED_TO": admin_id,
                                "ASSIGNMENT_DATE": datetime.now(),
                                "ASSIGNMENT_TIME": datetime.now(),
                                "STATUS": "BEKLEMEDE",
                                "COMMENTS": "Form oluşturuldu ve admin kullanıcısına atandı.",
                                "assigned_by_name": f"{user['NAME']} {user['SURNAME']}" if user else "Unknown User",
                                "assigned_to_name": "Admin User",
                                "escalated_to_name": None
                            }

                            db.mock_db["assignments"].append(new_assignment)

                            return form_id
                        elif f_name == 'update_form_status':
                            form_id, new_status, assigned_to, comments, current_user_id = args

                            for form in db.mock_db["forms"]:
                                if form["ID"] == form_id:
                                    form["form_status"] = new_status
                                    break

                            current_user = None
                            for u in db.mock_db["users"]:
                                if u["ID"] == current_user_id:
                                    current_user = u
                                    break

                            assigned_user = None
                            for u in db.mock_db["users"]:
                                if u["ID"] == int(assigned_to):
                                    assigned_user = u
                                    break

                            new_assignment = {
                                "ID": len(db.mock_db["assignments"]) + 1,
                                "FORM_ID": form_id,
                                "ASSIGNED_BY": current_user_id,
                                "ASSIGNED_TO": int(assigned_to),
                                "ASSIGNMENT_DATE": datetime.now(),
                                "ASSIGNMENT_TIME": datetime.now(),
                                "STATUS": new_status,
                                "COMMENTS": comments,
                                "assigned_by_name": f"{current_user['NAME']} {current_user['SURNAME']}" if current_user else "Unknown User",
                                "assigned_to_name": f"{assigned_user['NAME']} {assigned_user['SURNAME']}" if assigned_user else "Unknown User",
                                "escalated_to_name": None
                            }

                            db.mock_db["assignments"].append(new_assignment)

                            return True
                        elif f_name == 'get_form_pictures':
                            return [p for p in db.mock_db["pictures"] if p["FORM_ID"] == args[0]]
                        elif f_name == 'get_db_connection':
                            # Mock bağlantı nesnesi
                            class MockConnection:
                                def __init__(self):
                                    self.closed = False

                                def cursor(self):
                                    return MockCursor()

                                def commit(self):
                                    pass

                                def close(self):
                                    self.closed = True

                            class MockCursor:
                                def __init__(self):
                                    self.rowcount = 1

                                def execute(self, query, params=None):
                                    print(f"Mock execute: {query}")
                                    if "UPDATE ISG_FORMS SET TYPE" in query:
                                        form_id = params[1]
                                        new_type = params[0]
                                        for form in db.mock_db["forms"]:
                                            if form["ID"] == form_id:
                                                form["TYPE"] = new_type
                                                return True
                                    elif "UPDATE ISG_FORMS SET incident_loc" in query:
                                        form_id = params[1]
                                        new_loc = params[0]
                                        for form in db.mock_db["forms"]:
                                            if form["ID"] == form_id:
                                                form["incident_loc"] = new_loc
                                                return True
                                    elif "INSERT INTO USER_LOGS" in query:
                                        # Yeni log ID'si oluştur
                                        new_id = max([log["ID"] for log in db.mock_db.get("logs", [])], default=0) + 1

                                        # Parametreleri al
                                        user_id, username, log_type, log_description, ip_address, user_agent, related_id, additional_data = params

                                        # Yeni log kaydı oluştur
                                        new_log = {
                                            "ID": new_id,
                                            "USER_ID": user_id,
                                            "USERNAME": username,
                                            "LOG_TYPE": log_type,
                                            "LOG_DESCRIPTION": log_description,
                                            "IP_ADDRESS": ip_address,
                                            "USER_AGENT": user_agent,
                                            "RELATED_ID": related_id,
                                            "ADDITIONAL_DATA": additional_data,
                                            "CREATED_AT": datetime.now()
                                        }

                                        # Kullanıcı adını ekle
                                        for user in db.mock_db["users"]:
                                            if user["ID"] == user_id:
                                                new_log["user_fullname"] = f"{user['NAME']} {user['SURNAME']}"
                                                break

                                        # Log listesine ekle
                                        if "logs" not in db.mock_db:
                                            db.mock_db["logs"] = []
                                        db.mock_db["logs"].append(new_log)
                                        return True
                                    return True

                                def fetchone(self):
                                    return [10]  # Varsayılan maksimum uzunluk

                                def fetchall(self):
                                    return []

                            return MockConnection()
                        elif f_name == 'execute_query':
                            query = args[0] if args else ""
                            params = args[1] if len(args) > 1 else None
                            fetch = args[2] if len(args) > 2 else True

                            # USER_LOGS tablosuna ekleme sorgusu
                            if "INSERT INTO USER_LOGS" in query and not fetch:
                                # Yeni log ID'si oluştur
                                new_id = max([log["ID"] for log in db.mock_db.get("logs", [])], default=0) + 1

                                # Parametreleri al
                                user_id, username, log_type, log_description, ip_address, user_agent, related_id, additional_data = params

                                # Yeni log kaydı oluştur
                                new_log = {
                                    "ID": new_id,
                                    "USER_ID": user_id,
                                    "USERNAME": username,
                                    "LOG_TYPE": log_type,
                                    "LOG_DESCRIPTION": log_description,
                                    "IP_ADDRESS": ip_address,
                                    "USER_AGENT": user_agent,
                                    "RELATED_ID": related_id,
                                    "ADDITIONAL_DATA": additional_data,
                                    "CREATED_AT": datetime.now()
                                }

                                # Kullanıcı adını ekle
                                for user in db.mock_db["users"]:
                                    if user["ID"] == user_id:
                                        new_log["user_fullname"] = f"{user['NAME']} {user['SURNAME']}"
                                        break

                                # Log listesine ekle
                                if "logs" not in db.mock_db:
                                    db.mock_db["logs"] = []
                                db.mock_db["logs"].append(new_log)
                                return 1  # Etkilenen satır sayısı

                            # USER_LOGS tablosundan sorgulama
                            elif "SELECT" in query and "FROM USER_LOGS" in query:
                                # Tüm logları getir
                                logs = db.mock_db.get("logs", [])

                                # Filtreleme
                                if params:
                                    filtered_logs = []
                                    for log in logs:
                                        include = True
                                        param_index = 0

                                        if "LOG_TYPE = ?" in query and param_index < len(params):
                                            if log["LOG_TYPE"] != params[param_index]:
                                                include = False
                                            param_index += 1

                                        if "USER_ID = ?" in query and param_index < len(params):
                                            if log["USER_ID"] != params[param_index]:
                                                include = False
                                            param_index += 1

                                        if include:
                                            filtered_logs.append(log)

                                    logs = filtered_logs

                                # Sıralama
                                if "ORDER BY" in query and "DESC" in query:
                                    logs = sorted(logs, key=lambda x: x["CREATED_AT"], reverse=True)

                                # Limit
                                if "FETCH NEXT" in query:
                                    import re
                                    limit_match = re.search(r'FETCH NEXT (\d+) ROWS', query)
                                    if limit_match:
                                        limit = int(limit_match.group(1))
                                        logs = logs[:limit]

                                return logs

                            return []

                    return mock_function

                # Yeni mock fonksiyonu set et
                setattr(db, func_name, create_mock_function(func_name))

        override_db_functions()
        print("Mock veritabanı fonksiyonları hazır.")

except Exception as e:
    print(f"Hata: {e}")
    print("Mock veritabanı kullanılacak.")
    sys.exit(1)

# Oturum kontrolü için yardımcı fonksiyonlar
def login_required(view):
    def wrapped_view(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return view(*args, **kwargs)
    wrapped_view.__name__ = view.__name__
    return wrapped_view

def is_admin():
    """Kullanıcının admin yetkisine sahip olup olmadığını kontrol eder"""
    permission = session.get('permission', '')
    if isinstance(permission, str):
        return permission.strip() == 'ADMIN'
    return False

def get_user_permission():
    """Kullanıcının yetkisini döndürür (ADMIN, USER, NULL)"""
    permission = session.get('permission', '')
    if isinstance(permission, str):
        permission = permission.strip()
        if permission == 'ADMIN':
            return 'ADMIN'
        elif permission == 'USER':
            return 'USER'
        elif permission == 'NULL':
            return 'NULL'
    return None

def admin_required(view):
    """Admin yetkisi gerektiren sayfalar için dekoratör"""
    def wrapped_view(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        if not is_admin():
            flash('Bu sayfaya erişim yetkiniz bulunmamaktadır!', 'danger')
            return redirect(url_for('dashboard'))
        return view(*args, **kwargs)
    wrapped_view.__name__ = view.__name__
    return wrapped_view

@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        user = db.get_user_by_username(username)

        if user and (user['PASS'] == password or check_password_hash(user['PASS'], password)):
            # Kullanıcı durumunu kontrol et
            if user['STATUS'] and 'active' in user['STATUS']:
                # Kullanıcı yetkisini kontrol et
                if not user['PERMISSION'] or user['PERMISSION'].strip() == 'NULL':
                    flash('Bu hesabın sisteme giriş yetkisi bulunmamaktadır. Lütfen sistem yöneticisiyle iletişime geçin.', 'danger')
                    return render_template('login.html')

                session['user_id'] = user['ID']
                session['username'] = user['USERN']
                session['name'] = f"{user['NAME']} {user['SURNAME']}"
                session['permission'] = user['PERMISSION']

                # Başarılı giriş logla
                logger.log_login(user['ID'], user['USERN'], success=True)

                flash('Başarıyla giriş yaptınız!', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('Hesabınız devre dışı bırakılmıştır. Lütfen sistem yöneticisiyle iletişime geçin.', 'danger')
        else:
            flash('Geçersiz kullanıcı adı veya şifre!', 'danger')

    return render_template('login.html')

@app.route('/logout')
def logout():
    # Kullanıcı bilgilerini al
    user_id = session.get('user_id')
    username = session.get('username')

    # Çıkış işlemini logla
    if user_id and username:
        logger.log_logout(user_id, username)

    session.clear()
    flash('Başarıyla çıkış yaptınız!', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Son 5 formu getir (yetki kontrolü ile)
    is_admin_user = is_admin()
    recent_forms = db.get_all_forms(5, session['user_id'], is_admin_user)

    # Mevcut zamanı formatla
    current_time = datetime.now().strftime('%d.%m.%Y %H:%M')

    return render_template('dashboard.html', forms=recent_forms, is_admin=is_admin_user, current_time=current_time)

@app.route('/forms')
@login_required
def form_list():
    # Formları getir (yetki kontrolü ile)
    is_admin_user = is_admin()
    forms = db.get_all_forms(limit=100, user_id=session['user_id'], is_admin=is_admin_user)
    return render_template('form_list.html', forms=forms, is_admin=is_admin_user)

@app.route('/forms/<int:form_id>')
@login_required
def form_detail(form_id):
    # Yetki kontrolü
    is_admin_user = is_admin()

    # Form detaylarını getir (yetki kontrolü ile)
    form = db.get_form_by_id(form_id, session['user_id'], is_admin_user)

    if not form:
        flash('Form bulunamadı veya bu forma erişim yetkiniz yok!', 'danger')
        return redirect(url_for('form_list'))

    assignments = db.get_form_assignments(form_id)
    pictures = db.get_form_pictures(form_id)
    attachments = db.get_form_attachments(form_id)
    users = db.get_all_users()

    # Form tiplerini getir
    form_types = db.get_form_types()

    # Son atanan kişiyi belirle
    last_assigned_to = None
    if assignments and len(assignments) > 0:
        last_assigned_to = assignments[0]['ASSIGNED_TO']  # İlk kayıt en son atama kaydıdır

    return render_template('form_detail.html',
                          form=form,
                          assignments=assignments,
                          pictures=pictures,
                          attachments=attachments,
                          users=users,
                          form_types=form_types,
                          last_assigned_to=last_assigned_to,
                          is_admin=is_admin_user)

@app.route('/forms/create', methods=['GET', 'POST'])
@login_required
def form_create():
    if request.method == 'POST':
        form_data = {
            'type': request.form['type'],
            'username': session['username'],
            'duty': request.form['duty'],
            'incident_date': request.form['incident_date'],
            'incident_time': request.form['incident_time'],
            'incident_loc': request.form['incident_loc'],
            'description': request.form['description']
        }

        form_id = db.create_form(form_data, session['user_id'], DEFAULT_ADMIN_ID)

        if form_id:
            # Form oluşturma işlemini logla
            logger.log_form_create(
                session['user_id'],
                session['username'],
                form_id,
                form_data['type']
            )

            flash('Form başarıyla oluşturuldu!', 'success')
            return redirect(url_for('form_detail', form_id=form_id))
        else:
            flash('Form oluşturulurken bir hata oluştu!', 'danger')

    form_types = db.get_form_types()
    return render_template('form_create.html', form_types=form_types)

def allowed_file(filename):
    """Dosya uzantısının izin verilen uzantılardan olup olmadığını kontrol eder"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/uploads/<path:filename>')
@login_required
def download_file(filename):
    """Yüklenen dosyaları indirmek için kullanılır"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/forms/<int:form_id>/update', methods=['POST'])
@login_required
def form_update(form_id):
    try:
        # Yetki kontrolü
        is_admin_user = is_admin()

        # Kullanıcının bu formu güncelleme yetkisi var mı kontrol et
        form = db.get_form_by_id(form_id, session['user_id'], is_admin_user)

        if not form:
            flash('Form bulunamadı veya bu formu güncelleme yetkiniz yok!', 'danger')
            return redirect(url_for('form_list'))

        new_status = request.form['status']
        assigned_to = request.form['assigned_to']
        comments = request.form['comments'].strip()  # Boşlukları temizle

        print(f"Form güncelleme bilgileri: form_id={form_id}, status={new_status}, assigned_to={assigned_to}, comments={comments}")

        # Son atama kaydını kontrol et
        assignments = db.get_form_assignments(form_id)

        # Değişiklik var mı kontrol et
        has_changes = True
        if assignments:
            last_assignment = assignments[0]  # İlk kayıt en son atama kaydıdır

            # Durum, atanan kişi ve yorumlar aynı mı kontrol et
            if (str(last_assignment['STATUS']) == new_status and
                str(last_assignment['ASSIGNED_TO']) == assigned_to and
                not comments):
                has_changes = False
                print("Değişiklik yok, yeni atama kaydı oluşturulmayacak.")

        # Dosya yükleme işlemi
        uploaded_files = request.files.getlist('attachments')
        has_files = any(file and file.filename for file in uploaded_files)

        # Değişiklik yoksa ve dosya da yüklenmediyse işlem yapma
        if not has_changes and not has_files:
            flash('Herhangi bir değişiklik yapılmadı.', 'info')
            return redirect(url_for('form_detail', form_id=form_id))

        # Form durumunu güncelle ve yeni atama kaydı oluştur
        result = db.update_form_status(form_id, new_status, assigned_to, comments, session['user_id'])

        if result is None:
            flash('Form güncellenirken bir hata oluştu!', 'danger')
            return redirect(url_for('form_detail', form_id=form_id))

        # Form güncelleme işlemini logla
        logger.log_form_update(
            session['user_id'],
            session['username'],
            form_id,
            new_status,
            assigned_to
        )

        # Son eklenen atama kaydının ID'sini al
        assignments = db.get_form_assignments(form_id)
        if not assignments:
            flash('Form güncellendi ancak atama kaydı bulunamadı!', 'warning')
            return redirect(url_for('form_detail', form_id=form_id))

        assignment_id = assignments[0]['ID']

        # Dosya yükleme işlemi
        for file in uploaded_files:
            if file and file.filename and allowed_file(file.filename):
                # Güvenli dosya adı oluştur
                original_filename = secure_filename(file.filename)
                file_extension = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else ''
                stored_filename = f"{uuid.uuid4().hex}.{file_extension}" if file_extension else f"{uuid.uuid4().hex}"

                # Dosyayı kaydet
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], stored_filename)
                file.save(file_path)

                # Dosya bilgilerini veritabanına kaydet
                file_size = os.path.getsize(file_path)
                file_type = file.content_type if hasattr(file, 'content_type') else f"application/{file_extension}"

                db.create_form_attachment(
                    form_id=form_id,
                    assignment_id=assignment_id,
                    original_filename=original_filename,
                    stored_filename=stored_filename,
                    file_size=file_size,
                    file_type=file_type,
                    user_id=session['user_id']
                )

        if has_changes:
            flash('Form başarıyla güncellendi!', 'success')
        else:
            flash('Dosyalar başarıyla yüklendi!', 'success')
    except Exception as e:
        print(f"Form güncelleme hatası (app.py): {e}")
        flash(f'Form güncellenirken bir hata oluştu: {str(e)}', 'danger')

    return redirect(url_for('form_detail', form_id=form_id))

@app.template_filter('format_date')
def format_date(value):
    if value:
        return value.strftime('%d.%m.%Y')
    return ''

@app.template_filter('format_time')
def format_time(value):
    if value:
        return value.strftime('%H:%M')
    return ''

@app.template_filter('get_thumbnail_path')
def get_thumbnail_path(picture_path):
    """Resim yolundan thumbnail yolunu oluşturur"""
    if picture_path:
        return url_for('get_thumbnail', filename=picture_path)
    return ''

@app.route('/thumbnails/<path:filename>')
def get_thumbnail(filename):
    """Küçük resimleri (thumbnails) göstermek için kullanılır"""
    return send_from_directory('C:/BAYRAKTAR/ISG/MOBILE/pic/thumbnails', filename)

@app.route('/images/<path:filename>')
def get_image(filename):
    """Orijinal resimleri göstermek için kullanılır"""
    return send_from_directory('C:/BAYRAKTAR/ISG/MOBILE/pic', filename)

@app.route('/create-admin', methods=['GET', 'POST'])
def create_admin():
    if request.method == 'POST':
        name = request.form['name']
        surname = request.form['surname']
        username = request.form['username']
        password = generate_password_hash(request.form['password'], method='pbkdf2:sha256:1000000')

        # Admin kullanıcısı oluştur
        query = """
        INSERT INTO USERS (NAME, SURNAME, USERN, PASS, PERMISSION, STATUS)
        VALUES (%s, %s, %s, %s, 'ADMIN', 'active')
        """

        try:
            conn = db.get_db_connection()
            if conn:
                cursor = conn.cursor()
                cursor.execute(query, (name, surname, username, password))
                conn.commit()

                # Oluşturulan kullanıcının ID'sini al
                cursor.execute("SELECT TOP 1 ID FROM USERS ORDER BY ID DESC")
                user_id = cursor.fetchone()[0]

                conn.close()

                flash(f'Admin kullanıcısı başarıyla oluşturuldu! ID: {user_id}', 'success')
                return redirect(url_for('login'))
            else:
                flash('Veritabanı bağlantısı kurulamadı!', 'danger')
        except Exception as e:
            flash(f'Hata: {str(e)}', 'danger')

    return render_template('create_admin.html')

@app.route('/users')
@login_required
@admin_required
def user_list():
    """Kullanıcı yönetimi sayfası - sadece admin kullanıcılar erişebilir"""
    users = db.get_all_users_for_management()

    # Hata ayıklama: Kullanıcı listesini konsola yazdır
    print(f"Kullanıcı listesi: {users}")
    print(f"Kullanıcı sayısı: {len(users) if users else 0}")

    # Kullanıcı listesi boşsa, doğrudan veritabanından tüm kullanıcıları getirmeyi dene
    if not users:
        try:
            conn = db.get_db_connection()
            if conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM USERS")
                user_count = cursor.fetchone()[0]
                print(f"Veritabanındaki toplam kullanıcı sayısı: {user_count}")

                cursor.execute("SELECT TOP 10 * FROM USERS")
                rows = cursor.fetchall()
                for row in rows:
                    print(f"Kullanıcı: {row}")

                conn.close()
        except Exception as e:
            print(f"Veritabanı sorgusu hatası: {e}")

    return render_template('user_management.html', users=users)

@app.route('/users/create', methods=['POST'])
@login_required
@admin_required
def user_create():
    """Yeni kullanıcı oluşturma - sadece admin kullanıcılar erişebilir"""
    try:
        name = request.form['name']
        surname = request.form['surname']
        username = request.form['username']
        raw_password = request.form['password']  # Şifrelenmiş olmayan şifre
        password = generate_password_hash(raw_password, method='pbkdf2:sha256:1000000')
        email = request.form['email']
        permission = request.form['permission']

        # Kullanıcı adı kontrolü
        existing_user = db.get_user_by_username(username)
        if existing_user:
            flash('Bu kullanıcı adı zaten kullanılıyor!', 'danger')
            return redirect(url_for('user_list'))

        # Yeni kullanıcı oluştur
        user_id = db.create_user(name, surname, username, password, email, permission)

        if user_id:
            # Kullanıcı oluşturma işlemini logla
            logger.log_user_create(
                session['user_id'],
                session['username'],
                user_id,
                username,
                permission
            )

            # Kullanıcıya bilgilendirme e-postası gönder
            if email:
                try:
                    notifications.send_new_user_notification(
                        user_id=user_id,
                        name=name,
                        surname=surname,
                        username=username,
                        email=email,
                        permission=permission,
                        password=raw_password  # Şifreyi açık metin olarak gönder
                    )
                    print(f"Kullanıcı bilgilendirme e-postası gönderildi: {email}")
                except Exception as email_error:
                    print(f"Kullanıcı bilgilendirme e-postası gönderme hatası: {email_error}")
                    # E-posta hatası kullanıcı oluşturmayı etkilemesin

            flash(f'Kullanıcı başarıyla oluşturuldu! ID: {user_id}', 'success')
        else:
            flash('Kullanıcı oluşturulurken bir hata oluştu!', 'danger')
    except Exception as e:
        flash(f'Hata: {str(e)}', 'danger')

    return redirect(url_for('user_list'))

@app.route('/users/<int:user_id>/status', methods=['POST'])
@app.route('/users/<int:user_id>/status/', methods=['POST'])
@login_required
@admin_required
def user_update_status(user_id):
    """Kullanıcı durumunu güncelleme - sadece admin kullanıcılar erişebilir"""
    try:
        new_status = request.form['status']

        # Veritabanındaki sabit uzunluklu alan için boşluk ekle (CHAR(10) için)
        if len(new_status) < 10:
            new_status = new_status.ljust(10)

        # Kullanıcının kendisini devre dışı bırakmasını engelle
        if user_id == session['user_id'] and 'disabled' in new_status:
            flash('Kendi hesabınızı devre dışı bırakamazsınız!', 'danger')
            return redirect(url_for('user_list'))

        result = db.update_user_status(user_id, new_status)

        if result:
            status_text = "etkinleştirildi" if 'active' in new_status else "devre dışı bırakıldı"

            # Kullanıcı bilgilerini al
            target_user = db.get_user_by_id(user_id)
            target_username = target_user['USERN'] if target_user else f"ID:{user_id}"

            # Kullanıcı durumu güncelleme işlemini logla
            logger.log_user_status_update(
                session['user_id'],
                session['username'],
                user_id,
                target_username,
                new_status
            )

            flash(f'Kullanıcı durumu başarıyla {status_text}!', 'success')

            # Kullanıcı pasif duruma getirildiğinde, aktif oturumunu sonlandır
            if 'disabled' in new_status:
                # Redis veya başka bir oturum deposu kullanılıyorsa, o kullanıcının tüm oturumlarını temizle
                # Bu örnekte basit bir yaklaşım kullanıyoruz
                print(f"Kullanıcı {user_id} pasif duruma getirildi. Aktif oturumları sonlandırılacak.")

                # Burada gerçek bir uygulamada, tüm aktif oturumları veritabanından veya oturum deposundan temizleme kodu olabilir
                # Örneğin: db.clear_user_sessions(user_id)
        else:
            flash('Kullanıcı durumu güncellenirken bir hata oluştu!', 'danger')
    except Exception as e:
        flash(f'Hata: {str(e)}', 'danger')

    return redirect(url_for('user_list'))

@app.route('/users/<int:user_id>/password', methods=['POST'])
@app.route('/users/<int:user_id>/password/', methods=['POST'])
@login_required
@admin_required
def user_reset_password(user_id):
    """Kullanıcı şifresini sıfırlama - sadece admin kullanıcılar erişebilir"""
    try:
        new_password = request.form['password']
        hashed_password = generate_password_hash(new_password, method='pbkdf2:sha256:1000000')

        result = db.update_user_password(user_id, hashed_password)

        if result:
            # Kullanıcı bilgilerini al
            target_user = db.get_user_by_id(user_id)
            target_username = target_user['USERN'] if target_user else f"ID:{user_id}"

            # Şifre sıfırlama işlemini logla
            logger.log_user_password_reset(
                session['user_id'],
                session['username'],
                user_id,
                target_username
            )

            # Kullanıcıya şifre değişikliği bildirimi gönder
            if target_user and 'EMAIL' in target_user and target_user['EMAIL']:
                try:
                    notifications.send_new_user_notification(
                        user_id=user_id,
                        name=target_user['NAME'],
                        surname=target_user['SURNAME'],
                        username=target_user['USERN'],
                        email=target_user['EMAIL'],
                        permission=target_user['PERMISSION'],
                        password=new_password  # Yeni şifreyi açık metin olarak gönder
                    )
                    print(f"Şifre sıfırlama bildirimi gönderildi: {target_user['EMAIL']}")
                except Exception as email_error:
                    print(f"Şifre sıfırlama bildirimi gönderme hatası: {email_error}")
                    # E-posta hatası şifre sıfırlama işlemini etkilemesin

            flash('Kullanıcı şifresi başarıyla güncellendi!', 'success')
        else:
            flash('Kullanıcı şifresi güncellenirken bir hata oluştu!', 'danger')
    except Exception as e:
        flash(f'Hata: {str(e)}', 'danger')

    return redirect(url_for('user_list'))

@app.route('/api/form-types', methods=['GET'])
@login_required
def get_form_types_api():
    """Form tiplerini getiren API endpoint'i"""
    try:
        form_types = db.get_form_types()
        return jsonify({'success': True, 'form_types': form_types})
    except Exception as e:
        print(f"Form tipleri getirme hatası: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/forms/<int:form_id>/update-field', methods=['POST'])
@login_required
def update_form_field(form_id):
    """Form alanlarını güncellemek için API endpoint'i"""
    # Sadece admin kullanıcıların erişimine izin ver
    if not is_admin():
        return jsonify({'success': False, 'message': 'Bu işlem için yetkiniz bulunmamaktadır!'}), 403

    try:
        # JSON verisini al
        data = request.json
        field_name = data.get('field')
        new_value = data.get('value')

        # Alan adı ve değer kontrolü
        if not field_name or not new_value:
            return jsonify({'success': False, 'message': 'Alan adı ve değer gereklidir!'}), 400

        # Form bilgilerini al
        form = db.get_form_by_id(form_id, None, True)  # Admin yetkisiyle formu al
        if not form:
            return jsonify({'success': False, 'message': 'Form bulunamadı!'}), 404

        # Form tipi için geçerlilik kontrolü
        if field_name == 'TYPE':
            # Form tiplerini veritabanından al
            form_types = db.get_form_types()
            valid_types = [ft['DISPLAY'] for ft in form_types]

            # Geçerli bir form tipi mi kontrol et
            if new_value not in valid_types:
                return jsonify({'success': False, 'message': 'Geçersiz form tipi!'}), 400

            # Not: ISG_FORMS tablosunda form tipi TYPE kolonunda tutuluyor,
            # form_type tablosunda ise DISPLAY kolonunda tutuluyor

        # Güncelleme işlemi
        conn = db.get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': 'Veritabanı bağlantısı kurulamadı!'}), 500

        cursor = conn.cursor()

        # Alan adına göre güncelleme yap
        if field_name == 'TYPE':
            # TYPE alanını güncelle - ISG_FORMS tablosunda form tipi TYPE kolonunda tutuluyor
            cursor.execute("UPDATE ISG_FORMS SET TYPE = ? WHERE ID = ?", (new_value, form_id))
        elif field_name == 'incident_loc':
            cursor.execute("UPDATE ISG_FORMS SET incident_loc = ? WHERE ID = ?", (new_value, form_id))
        else:
            conn.close()
            return jsonify({'success': False, 'message': 'Geçersiz alan adı!'}), 400

        conn.commit()
        conn.close()

        # Son atama kaydını al
        assignments = db.get_form_assignments(form_id)
        last_assigned_to = None
        if assignments and len(assignments) > 0:
            last_assigned_to = assignments[0]['ASSIGNED_TO']  # İlk kayıt en son atama kaydıdır
        else:
            last_assigned_to = form['userid']  # Atama yoksa form sahibine ata

        # Güncelleme kaydı oluştur - atanan kişiyi değiştirmeden
        log_message = f"{field_name} alanı '{new_value}' olarak güncellendi"
        db.update_form_status(form_id, form['status'], last_assigned_to, log_message, session['user_id'])

        # Form alanı güncelleme işlemini logla
        logger.log_field_update(
            session['user_id'],
            session['username'],
            form_id,
            field_name,
            new_value
        )

        # Alan güncellemesi için bildirim gönder
        try:
            # Atanan kullanıcı bilgilerini al
            assigned_user = db.get_user_by_id(last_assigned_to)
            # Güncelleyen kullanıcı bilgilerini al
            current_user = db.get_user_by_id(session['user_id'])

            if assigned_user and current_user:
                # Eski değeri al
                old_value = form[field_name] if field_name in form else ""

                # Bildirim gönder
                notifications.send_field_update_notification(
                    form_id=form_id,
                    form_type=form['TYPE'],
                    field_name=field_name,
                    old_value=old_value,
                    new_value=new_value,
                    assigned_user=assigned_user,
                    current_user=current_user
                )

                # İlk güncelleme ise form oluşturan kişiye de bildirim gönder
                if form['userid'] != session['user_id']:  # Form oluşturan kişi güncelleyen kişi değilse
                    # Form oluşturan kullanıcının bilgilerini al
                    creator_user = db.get_user_by_id(form['userid'])

                    if creator_user:
                        # Atama geçmişini kontrol et - ilk güncelleme mi?
                        if len(assignments) <= 1:  # Sadece ilk atama kaydı varsa (form oluşturma)
                            notifications.send_first_update_notification(
                                form_id=form_id,
                                form_type=form['TYPE'],
                                update_type="Alan Güncellemesi",
                                creator_user=creator_user,
                                current_user=current_user,
                                details=f"{field_name} alanı '{old_value}' değerinden '{new_value}' değerine güncellendi."
                            )
        except Exception as e:
            print(f"Alan güncelleme bildirimi gönderme hatası: {e}")
            # Bildirim hatası form güncelleme işlemini etkilemesin

        return jsonify({'success': True, 'message': 'Alan başarıyla güncellendi!'})
    except Exception as e:
        print(f"Form alanı güncelleme hatası: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/logs')
@login_required
@admin_required
def logs():
    """Sistem loglarını görüntüleme sayfası - sadece admin kullanıcılar erişebilir"""
    # Filtreleri al
    log_type = request.args.get('log_type')
    user_id = request.args.get('user_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    limit = request.args.get('limit', '100')

    # Limit değerini sayıya çevir
    try:
        limit = int(limit)
    except (ValueError, TypeError):
        limit = 100

    # Tarih formatını düzenle
    if start_date:
        start_date = f"{start_date} 00:00:00"
    if end_date:
        end_date = f"{end_date} 23:59:59"

    # User ID'yi sayıya çevir
    if user_id:
        try:
            user_id = int(user_id)
        except (ValueError, TypeError):
            user_id = None

    # Logları getir
    logs = logger.get_logs(limit=limit, log_type=log_type, user_id=user_id,
                          start_date=start_date, end_date=end_date)

    # Kullanıcı listesini getir
    users = db.get_all_users_for_management()

    return render_template('logs.html', logs=logs, users=users)

@app.route('/reminder-settings')
@login_required
@admin_required
def reminder_settings():
    """Hatırlatıcı ayarları sayfası - sadece admin kullanıcılar erişebilir"""
    # Tüm hatırlatıcı ayarlarını getir
    settings_list = db.get_reminder_settings()

    # Ayarları bir sözlük olarak düzenle
    settings = {}
    for setting in settings_list:
        settings[setting['SETTING_NAME']] = setting

    return render_template('reminder_settings.html', settings=settings)

@app.route('/update-reminder-setting', methods=['POST'])
@login_required
@admin_required
def update_reminder_setting():
    """Hatırlatıcı ayarını günceller"""
    setting_name = request.form.get('setting_name')
    setting_value = request.form.get('setting_value')
    enabled = 1 if request.form.get('enabled') else 0

    if not setting_name or not setting_value:
        flash('Ayar adı ve değeri gereklidir!', 'danger')
        return redirect(url_for('reminder_settings'))

    # Ayarı güncelle
    db.update_reminder_setting(setting_name, setting_value, session['user_id'])

    # Etkinlik durumunu güncelle
    db.toggle_reminder_setting(setting_name, enabled, session['user_id'])

    # İşlemi logla
    logger.log_system_action(
        session['user_id'],
        session['username'],
        'REMINDER_SETTING_UPDATE',
        f"Hatırlatıcı ayarı güncellendi: {setting_name} = {setting_value}, Etkin: {'Evet' if enabled else 'Hayır'}"
    )

    flash('Hatırlatıcı ayarı başarıyla güncellendi!', 'success')
    return redirect(url_for('reminder_settings'))

@app.route('/run-reminder-now', methods=['POST'])
@login_required
@admin_required
def run_reminder_now():
    """Hatırlatıcıyı hemen çalıştırır"""
    reminder_type = request.form.get('reminder_type')

    if reminder_type == 'unassigned':
        result = reminders.send_unassigned_forms_reminder()
        message = 'Atanmamış form hatırlatıcısı'
    elif reminder_type == 'pending':
        result = reminders.send_pending_forms_reminder()
        message = 'Bekleyen form hatırlatıcısı'
    else:
        flash('Geçersiz hatırlatıcı türü!', 'danger')
        return redirect(url_for('reminder_settings'))

    # İşlemi logla
    logger.log_system_action(
        session['user_id'],
        session['username'],
        'REMINDER_MANUAL_RUN',
        f"{message} manuel olarak çalıştırıldı. Sonuç: {'Başarılı' if result else 'Başarısız'}"
    )

    if result:
        flash(f'{message} başarıyla gönderildi!', 'success')
    else:
        flash(f'{message} gönderilirken bir hata oluştu!', 'danger')

    return redirect(url_for('reminder_settings'))

@app.template_filter('format_datetime')
def format_datetime(value):
    """Tarih ve saat formatını düzenler"""
    if value:
        return value.strftime('%d.%m.%Y %H:%M:%S')
    return ''

@app.route('/profile')
@login_required
def profile():
    """Kullanıcı profil sayfası"""
    # Kullanıcı bilgilerini getir
    user = db.get_user_by_id(session['user_id'])
    if not user:
        flash('Kullanıcı bilgileri bulunamadı!', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('profile.html', user=user)

@app.route('/profile/update-password', methods=['POST'])
@login_required
def update_password():
    """Kullanıcının kendi şifresini güncellemesi"""
    try:
        current_password = request.form['current_password']
        new_password = request.form['new_password']
        confirm_password = request.form['confirm_password']

        # Kullanıcı bilgilerini getir
        user = db.get_user_by_id(session['user_id'])
        if not user:
            flash('Kullanıcı bilgileri bulunamadı!', 'danger')
            return redirect(url_for('profile'))

        # Mevcut şifre kontrolü
        if not check_password_hash(user['PASS'], current_password) and user['PASS'] != current_password:
            flash('Mevcut şifreniz hatalı!', 'danger')
            return redirect(url_for('profile'))

        # Yeni şifre kontrolü
        if len(new_password) < 8:
            flash('Yeni şifreniz en az 8 karakter uzunluğunda olmalıdır!', 'danger')
            return redirect(url_for('profile'))

        # Şifre eşleşme kontrolü
        if new_password != confirm_password:
            flash('Yeni şifre ve tekrarı eşleşmiyor!', 'danger')
            return redirect(url_for('profile'))

        # Şifreyi güncelle
        hashed_password = generate_password_hash(new_password, method='pbkdf2:sha256:1000000')
        result = db.update_user_password(session['user_id'], hashed_password)

        if result:
            # Şifre değiştirme işlemini logla
            logger.log_user_password_change(
                session['user_id'],
                session['username']
            )

            flash('Şifreniz başarıyla güncellendi!', 'success')
        else:
            flash('Şifre güncellenirken bir hata oluştu!', 'danger')
    except Exception as e:
        flash(f'Hata: {str(e)}', 'danger')

    return redirect(url_for('profile'))

if __name__ == '__main__':
    # Hatırlatıcı zamanlayıcısını başlat
    reminders.start_reminder_scheduler()

    # Uygulamayı başlat
    app.run(host='0.0.0.0', port=4000)
