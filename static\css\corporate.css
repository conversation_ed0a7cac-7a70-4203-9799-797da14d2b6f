/* Corporate ERP Theme - Premium Flat Design */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --text-color: #333;
    --text-muted: #7f8c8d;
    --border-color: #ddd;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --sidebar-width: 250px;
    --header-height: 60px;
    --font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* General Styles */
body {
    font-family: var(--font-family);
    color: var(--text-color);
    background-color: #f5f7fa;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    max-width: 1400px;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--primary-color);
}

h1 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
}

.text-muted {
    color: var(--text-muted) !important;
}

/* Navbar */
.navbar {
    background-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 0;
    height: var(--header-height);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.2rem;
    padding: 0.5rem 1rem;
    color: white !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 1rem 1rem;
    transition: all 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover,
.navbar-dark .navbar-nav .nav-link:focus {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .navbar-nav .active > .nav-link {
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
}

/* Cards */
.card {
    border: none;
    border-radius: 6px;
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Equal height cards */
.equal-height-cards .card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.equal-height-cards .card-body {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
}

.equal-height-cards .card-body .mt-auto {
    margin-top: auto !important;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn {
    border-radius: 4px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    border: none;
}

.btn-primary {
    background-color: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-success {
    background-color: var(--success-color);
}

.btn-success:hover {
    background-color: #27ae60;
}

.btn-warning {
    background-color: var(--warning-color);
}

.btn-warning:hover {
    background-color: #e67e22;
}

.btn-danger {
    background-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-info {
    background-color: var(--info-color);
}

.btn-info:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: #2c3e50;
}

.btn-outline-primary {
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
}

.btn-outline-primary:hover {
    background-color: var(--accent-color);
    color: white;
}

/* Forms */
.form-control, .form-select {
    border-radius: 4px;
    border: 1px solid var(--border-color);
    padding: 0.5rem 0.75rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* Tables */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.table th {
    font-weight: 600;
    color: var(--primary-color);
    border-top: none;
    border-bottom: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
}

.table td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
    border-top: 1px solid var(--border-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 4px;
}

.bg-primary {
    background-color: var(--accent-color) !important;
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

.bg-info {
    background-color: var(--info-color) !important;
}

/* Footer */
footer {
    background-color: var(--light-color);
    color: var(--text-muted);
    padding: 1.5rem 0;
    margin-top: auto;
}

/* Utilities */
.shadow {
    box-shadow: var(--card-shadow) !important;
}

/* File Icons */
.file-item {
    transition: transform 0.2s ease;
}

.file-item:hover {
    transform: translateY(-3px);
}

.file-name {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Timeline for assignments */
.timeline .card {
    border-left: 3px solid var(--accent-color);
}

/* Login Page */
.login-container {
    min-height: calc(100vh - var(--header-height) - 80px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    max-width: 400px;
    width: 100%;
}

.login-header {
    text-align: center;
    padding: 2rem 1rem;
}

.login-logo {
    max-width: 80px;
    margin-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .card-body {
        padding: 1.25rem;
    }

    h1 {
        font-size: 1.5rem;
    }
}
