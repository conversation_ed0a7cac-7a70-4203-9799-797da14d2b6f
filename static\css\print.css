/* ISG Form Yazdırma CSS */

/* Yazdırma için genel ayar<PERSON> */
@media print {
    /* <PERSON><PERSON> ayarları */
    @page {
        size: A4;
        margin: 1cm;
    }
    
    /* Yazdırma sırasında gizlenecek öğeler */
    .navbar,
    .footer,
    .btn-back,
    .btn-print,
    .alert,
    footer,
    .no-print {
        display: none !important;
    }
    
    /* Yazdırma sırasında gösterilecek öğeler */
    .print-only {
        display: block !important;
    }
    
    /* Genel stil ayarları */
    body {
        font-size: 12pt;
        line-height: 1.3;
        background: #fff;
        color: #000;
        margin: 0;
        padding: 0;
        font-family: Arial, Helvetica, sans-serif;
    }
    
    /* Konteynır genişliği */
    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }
    
    /* Başlık stilleri */
    h1 {
        font-size: 18pt;
        margin-bottom: 10pt;
    }
    
    h2, h3, h4, h5 {
        font-size: 14pt;
        margin-bottom: 8pt;
    }
    
    /* <PERSON>rt stilleri */
    .card {
        border: 1px solid #ddd;
        margin-bottom: 15pt;
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    .card-header {
        background-color: #f5f5f5;
        padding: 8pt;
        font-weight: bold;
        border-bottom: 1px solid #ddd;
    }
    
    .card-body {
        padding: 8pt;
    }
    
    /* Tablo stilleri */
    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 10pt;
    }
    
    table, th, td {
        border: 1px solid #ddd;
    }
    
    th, td {
        padding: 5pt;
        text-align: left;
    }
    
    th {
        background-color: #f5f5f5;
        font-weight: bold;
    }
    
    /* Rozet stilleri */
    .badge {
        padding: 2pt 5pt;
        border-radius: 3pt;
        font-size: 9pt;
        font-weight: normal;
        color: #fff;
        display: inline-block;
    }
    
    .bg-warning {
        background-color: #f39c12 !important;
    }
    
    .bg-success {
        background-color: #2ecc71 !important;
    }
    
    .bg-danger {
        background-color: #e74c3c !important;
    }
    
    .bg-primary {
        background-color: #3498db !important;
    }
    
    .bg-info {
        background-color: #3498db !important;
    }
    
    /* Form bilgileri için grid düzeni */
    .print-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10pt;
    }
    
    /* Resimler için grid düzeni */
    .print-images {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10pt;
    }
    
    .print-image-item {
        text-align: center;
    }
    
    .print-image-item img {
        max-width: 100%;
        max-height: 150pt;
        object-fit: contain;
    }
    
    /* Atama geçmişi için zaman çizelgesi */
    .print-timeline {
        margin-top: 10pt;
    }
    
    .print-timeline-item {
        padding: 5pt;
        border-left: 2pt solid #3498db;
        margin-bottom: 5pt;
        padding-left: 10pt;
    }
    
    /* Sayfa sonu kontrolü */
    .page-break {
        page-break-before: always;
    }
    
    /* Yazdırma logosu */
    .print-header {
        text-align: center;
        margin-bottom: 15pt;
    }
    
    .print-logo {
        max-height: 50pt;
        margin-bottom: 5pt;
    }
    
    /* Yazdırma altbilgisi */
    .print-footer {
        text-align: center;
        margin-top: 15pt;
        font-size: 9pt;
        color: #777;
        border-top: 1pt solid #ddd;
        padding-top: 5pt;
    }
}
