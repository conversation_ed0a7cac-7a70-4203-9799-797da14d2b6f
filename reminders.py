"""
ISG Form Yönetim Sistemi - Hatırlatıcı Modülü
Bu modül, otomatik hatırlatıcılar için kullanılır.
"""

import db
import notifications
import logger as system_logger  # Bizim logger modülümüzü farklı isimle import et
from datetime import datetime, timedelta, date
import time
import threading
import schedule
import logging

# Loglama ayarları
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('reminders.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
reminder_logger = logging.getLogger('reminders')  # Python logging modülü için farklı isim

# StreamHandler'ın kodlamasını da UTF-8 olarak ayarla
for handler in reminder_logger.handlers:
    if isinstance(handler, logging.StreamHandler):
        handler.setStream(open(handler.stream.fileno(), mode='w', encoding='utf-8', buffering=1))

def send_unassigned_forms_reminder():
    """Henüz atanmamış formlar için hatırlatıcı gönderir"""
    try:
        # Ayarları kontrol et
        setting = db.get_reminder_setting('UNASSIGNED_FORM_REMINDER_EMAIL')
        if not setting or not setting.get('ENABLED', 0):
            reminder_logger.info("Atanmamış form hatırlatıcısı devre dışı.")
            return False

        # Son gönderim zamanını kontrol et
        last_sent = db.get_reminder_setting('LAST_UNASSIGNED_REMINDER_SENT')
        if last_sent:
            try:
                last_sent_time = datetime.strptime(last_sent['SETTING_VALUE'], '%Y-%m-%d %H:%M:%S')
                frequency_setting = db.get_reminder_setting('UNASSIGNED_FORM_REMINDER_FREQUENCY')
                frequency_hours = int(frequency_setting['SETTING_VALUE']) if frequency_setting else 24

                # Henüz gönderim zamanı gelmediyse çık
                if datetime.now() < last_sent_time + timedelta(hours=frequency_hours):
                    reminder_logger.info(f"Atanmamış form hatırlatıcısı için henüz zaman gelmedi. Son gönderim: {last_sent_time}")
                    return False
            except Exception as e:
                reminder_logger.error(f"Son gönderim zamanı kontrolü hatası: {e}")

        # Atanmamış formları getir
        unassigned_forms = db.get_unassigned_forms()
        if not unassigned_forms:
            reminder_logger.info("Atanmamış form bulunamadı.")
            # Son gönderim zamanını güncelle
            db.update_last_reminder_sent('LAST_UNASSIGNED_REMINDER_SENT')
            return False

        # E-posta adresini al
        to_email = setting['SETTING_VALUE']
        if not to_email:
            reminder_logger.error("Atanmamış form hatırlatıcısı için e-posta adresi bulunamadı.")
            return False

        # E-posta içeriğini oluştur
        subject = f"ISG Form Sistemi - Atanmamış Form Hatırlatıcısı ({len(unassigned_forms)} form)"

        # Form listesini oluştur
        form_list_html = ""
        for form in unassigned_forms:
            form_date = form['date'].strftime('%d.%m.%Y') if form['date'] else 'Bilinmiyor'
            form_list_html += f"""
            <tr>
                <td>{form['ID']}</td>
                <td>{form['TYPE']}</td>
                <td>{form_date}</td>
                <td>{form['user_fullname'] if form['user_fullname'] else 'Bilinmiyor'}</td>
                <td>{form['incident_loc']}</td>
                <td><a href="http://isg.bayraktar.com:4000/forms/{form['ID']}">Görüntüle</a></td>
            </tr>
            """

        # E-posta içeriği
        message = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #f39c12; color: white; padding: 10px; text-align: center; }}
                .content {{ padding: 20px; border: 1px solid #ddd; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f5f5f5; }}
                tr:hover {{ background-color: #f9f9f9; }}
                .warning {{ color: #f39c12; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>ISG Form Sistemi - Atanmamış Form Hatırlatıcısı</h2>
                </div>
                <div class="content">
                    <p>Merhaba,</p>

                    <p>Aşağıdaki <span class="warning">{len(unassigned_forms)}</span> form henüz atanmamış durumda ve işlem bekliyor:</p>

                    <table>
                        <thead>
                            <tr>
                                <th>Form ID</th>
                                <th>Form Tipi</th>
                                <th>Oluşturma Tarihi</th>
                                <th>Oluşturan</th>
                                <th>Olay Yeri</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                            {form_list_html}
                        </tbody>
                    </table>

                    <p>Lütfen bu formları inceleyip gerekli atamaları yapınız.</p>

                    <p>Saygılarımızla,<br>ISG Form Sistemi</p>
                </div>
                <div class="footer">
                    <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayınız.</p>
                </div>
            </div>
        </body>
        </html>
        """

        # E-postayı gönder
        result = notifications.send_email(to_email, subject, message)

        if result:
            reminder_logger.info(f"Atanmamış form hatırlatıcısı başarıyla gönderildi: {to_email}")
            # Son gönderim zamanını güncelle
            db.update_last_reminder_sent('LAST_UNASSIGNED_REMINDER_SENT')

            # Sistem loguna kaydet
            form_ids = [form['ID'] for form in unassigned_forms]
            system_logger.log_reminder(
                reminder_type="UNASSIGNED_FORMS",
                description=f"Atanmamış {len(unassigned_forms)} form için hatırlatıcı gönderildi: {to_email}",
                success=True,
                related_ids=form_ids
            )
        else:
            reminder_logger.error(f"Atanmamış form hatırlatıcısı gönderme hatası")

            # Sistem loguna hata kaydet
            system_logger.log_reminder(
                reminder_type="UNASSIGNED_FORMS",
                description=f"Atanmamış form hatırlatıcısı gönderme hatası: {to_email}",
                success=False
            )

        return result
    except Exception as e:
        reminder_logger.error(f"Atanmamış form hatırlatıcısı hatası: {e}")
        return False

def send_pending_forms_reminder():
    """Belirli bir süredir bekleyen formlar için hatırlatıcı gönderir"""
    try:
        # Ayarları kontrol et
        setting = db.get_reminder_setting('PENDING_FORM_REMINDER_DAYS')
        if not setting or not setting.get('ENABLED', 0):
            reminder_logger.info("Bekleyen form hatırlatıcısı devre dışı.")
            return False

        # Gün sayısını al
        days = int(setting['SETTING_VALUE'])
        if days <= 0:
            reminder_logger.info("Bekleyen form hatırlatıcısı için gün sayısı geçersiz.")
            return False

        # Bekleyen formları getir
        pending_forms = db.get_pending_forms_older_than_days(days)
        if not pending_forms:
            reminder_logger.info(f"{days} günden eski bekleyen form bulunamadı.")
            return False

        # Formları atanan kişilere göre grupla
        forms_by_assignee = {}
        for form in pending_forms:
            if form['last_assigned_to']:
                assignee_id = form['last_assigned_to']
                if assignee_id not in forms_by_assignee:
                    forms_by_assignee[assignee_id] = []
                forms_by_assignee[assignee_id].append(form)

        # Her atanan kişiye e-posta gönder
        for assignee_id, forms in forms_by_assignee.items():
            # Kullanıcı bilgilerini al
            assignee = db.get_user_by_id(assignee_id)
            if not assignee or not assignee.get('EMAIL'):
                reminder_logger.warning(f"Kullanıcı bulunamadı veya e-posta adresi yok: {assignee_id}")
                continue

            # E-posta içeriğini oluştur
            subject = f"ISG Form Sistemi - Bekleyen Form Hatırlatıcısı ({len(forms)} form)"

            # Form listesini oluştur
            form_list_html = ""
            for form in forms:
                form_date = form['date'].strftime('%d.%m.%Y') if form['date'] else 'Bilinmiyor'
                # Tarih türü kontrolü ve dönüştürme
                if form['date']:
                    if isinstance(form['date'], datetime):
                        # Eğer datetime ise, sadece date kısmını al
                        form_date_obj = form['date'].date()
                    elif isinstance(form['date'], date):
                        # Eğer date ise, olduğu gibi kullan
                        form_date_obj = form['date']
                    else:
                        # Diğer türler için string'den parse etmeye çalış
                        try:
                            form_date_obj = datetime.strptime(str(form['date']), '%Y-%m-%d').date()
                        except:
                            form_date_obj = None

                    if form_date_obj:
                        # Şimdiki tarihi date olarak al ve farkı hesapla
                        today = datetime.now().date()
                        days_pending = (today - form_date_obj).days
                    else:
                        days_pending = 'Bilinmiyor'
                else:
                    days_pending = 'Bilinmiyor'
                form_list_html += f"""
                <tr>
                    <td>{form['ID']}</td>
                    <td>{form['TYPE']}</td>
                    <td>{form_date}</td>
                    <td>{days_pending} gün</td>
                    <td>{form['form_status']}</td>
                    <td><a href="http://isg.bayraktar.com:4000/forms/{form['ID']}">Görüntüle</a></td>
                </tr>
                """

            # E-posta içeriği
            message = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                    .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #e74c3c; color: white; padding: 10px; text-align: center; }}
                    .content {{ padding: 20px; border: 1px solid #ddd; }}
                    .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                    table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                    th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
                    th {{ background-color: #f5f5f5; }}
                    tr:hover {{ background-color: #f9f9f9; }}
                    .warning {{ color: #e74c3c; font-weight: bold; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h2>ISG Form Sistemi - Bekleyen Form Hatırlatıcısı</h2>
                    </div>
                    <div class="content">
                        <p>Sayın <strong>{assignee['NAME']} {assignee['SURNAME']}</strong>,</p>

                        <p>Size atanmış aşağıdaki <span class="warning">{len(forms)}</span> form <span class="warning">{days} gün veya daha uzun</span> süredir beklemede:</p>

                        <table>
                            <thead>
                                <tr>
                                    <th>Form ID</th>
                                    <th>Form Tipi</th>
                                    <th>Oluşturma Tarihi</th>
                                    <th>Bekleyen Süre</th>
                                    <th>Durum</th>
                                    <th>İşlem</th>
                                </tr>
                            </thead>
                            <tbody>
                                {form_list_html}
                            </tbody>
                        </table>

                        <p>Lütfen bu formları en kısa sürede inceleyip gerekli işlemleri yapınız.</p>

                        <p>Saygılarımızla,<br>ISG Form Sistemi</p>
                    </div>
                    <div class="footer">
                        <p>Bu e-posta otomatik olarak gönderilmiştir. Lütfen yanıtlamayınız.</p>
                    </div>
                </div>
            </body>
            </html>
            """

            # E-postayı gönder
            result = notifications.send_email(assignee['EMAIL'], subject, message)

            if result:
                reminder_logger.info(f"Bekleyen form hatırlatıcısı başarıyla gönderildi: {assignee['EMAIL']}")

                # Sistem loguna kaydet
                form_ids = [form['ID'] for form in forms]
                system_logger.log_reminder(
                    reminder_type="PENDING_FORMS",
                    description=f"Bekleyen {len(forms)} form için hatırlatıcı gönderildi: {assignee['NAME']} {assignee['SURNAME']} ({assignee['EMAIL']})",
                    success=True,
                    related_ids=form_ids
                )
            else:
                reminder_logger.error(f"Bekleyen form hatırlatıcısı gönderme hatası: {assignee['EMAIL']}")

                # Sistem loguna hata kaydet
                system_logger.log_reminder(
                    reminder_type="PENDING_FORMS",
                    description=f"Bekleyen form hatırlatıcısı gönderme hatası: {assignee['NAME']} {assignee['SURNAME']} ({assignee['EMAIL']})",
                    success=False
                )

        return True
    except Exception as e:
        reminder_logger.error(f"Bekleyen form hatırlatıcısı hatası: {e}")
        return False

def start_reminder_scheduler():
    """Hatırlatıcı zamanlayıcısını başlatır"""
    # Her gün saat 09:00'da atanmamış form hatırlatıcısı gönder
    schedule.every().day.at("09:00").do(send_unassigned_forms_reminder)

    # Her gün saat 10:00'da bekleyen form hatırlatıcısı gönder
    schedule.every().day.at("10:00").do(send_pending_forms_reminder)

    reminder_logger.info("Hatırlatıcı zamanlayıcısı başlatıldı.")

    # Zamanlayıcıyı ayrı bir thread'de çalıştır
    def run_scheduler():
        while True:
            schedule.run_pending()
            time.sleep(60)  # Her dakika kontrol et

    scheduler_thread = threading.Thread(target=run_scheduler)
    scheduler_thread.daemon = True  # Ana program sonlandığında thread de sonlanır
    scheduler_thread.start()

    return scheduler_thread

# Test fonksiyonu
def run_reminders_now():
    """Hatırlatıcıları hemen çalıştırır (test için)"""
    reminder_logger.info("Hatırlatıcılar manuel olarak çalıştırılıyor...")
    send_unassigned_forms_reminder()
    send_pending_forms_reminder()
    reminder_logger.info("Hatırlatıcılar çalıştırıldı.")
