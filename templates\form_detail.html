{% extends 'base.html' %}

{% block title %}Form Detayı - ISG Form Yönetim Si<PERSON>mi{% endblock %}

{% block extra_css %}
<style>
    .editable-field[data-editable="true"] {
        cursor: pointer;
        padding: 2px 5px;
        border-radius: 3px;
        transition: background-color 0.2s;
    }

    .editable-field[data-editable="true"]:hover {
        background-color: #f8f9fa;
    }

    .editable-field.editing {
        background-color: #fff;
        border: 1px solid #ced4da;
        padding: 5px;
        border-radius: 4px;
    }

    .editable-field-input {
        width: 100%;
        border: none;
        background: transparent;
        outline: none;
    }

    .editable-field-select {
        width: 100%;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 5px;
    }
</style>
<!-- Yazdırma CSS -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/print.css') }}" media="print">
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                Form #{{ form.ID }}
                {% if is_admin %}
                <span class="badge bg-danger">Admin Erişimi</span>
                {% else %}
                <span class="badge bg-info">Atanmış Form Erişimi</span>
                {% endif %}
            </h1>
            <div>
                <button id="printButton" class="btn btn-primary me-2 btn-print">
                    <i class="bi bi-printer"></i> Yazdır
                </button>
                <a href="{{ url_for('form_list') }}" class="btn btn-secondary btn-back">
                    <i class="bi bi-arrow-left"></i> Formlara Dön
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Form Bilgileri</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>Form Tipi:</strong> <span id="formType" class="editable-field" data-field="TYPE" data-form-id="{{ form.ID }}" {% if is_admin %}title="Düzenlemek için çift tıklayın" data-editable="true"{% endif %}>{{ form.TYPE }}</span></p>
                        <p><strong>Oluşturma Tarihi:</strong> {{ form.date|format_date }}</p>
                        <p><strong>Oluşturan:</strong> {{ form.user_fullname if form.user_fullname else 'Bilinmiyor' }}</p>
                        <p><strong>Görev:</strong> {{ form.duty }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Olay Tarihi:</strong> {{ form.incident_date|format_date }}</p>
                        <p><strong>Olay Saati:</strong> {{ form.incident_time|format_time }}</p>
                        <p><strong>Olay Yeri:</strong> <span id="incidentLocation" class="editable-field" data-field="incident_loc" data-form-id="{{ form.ID }}" {% if is_admin %}title="Düzenlemek için çift tıklayın" data-editable="true"{% endif %}>{{ form.incident_loc }}</span></p>
                        <p><strong>Durum:</strong>
                            <span class="badge {% if form.status == 'BEKLEMEDE' %}bg-warning{% elif form.status == 'TAMAMLANDI' %}bg-success{% elif form.status == 'REDDEDILDI' %}bg-danger{% else %}bg-primary{% endif %}">
                                {{ form.status }}
                            </span>
                        </p>
                    </div>
                </div>

                <div class="mb-3">
                    <h6>Açıklama:</h6>
                    <p>{{ form.description }}</p>
                </div>

                {% if form.ISG_description %}
                <div class="mb-3">
                    <h6>ISG Açıklaması:</h6>
                    <p>{{ form.ISG_description }}</p>
                </div>
                {% endif %}

                {% if form.suggestion %}
                <div class="mb-3">
                    <h6>Öneri:</h6>
                    <p>{{ form.suggestion }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Form Geçmişi -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Form Geçmişi</h5>
            </div>
            <div class="card-body">
                {% if assignments %}
                <div class="timeline">
                    {% for assignment in assignments %}
                    <div class="card mb-2 {% if 'alanı' in assignment.ASSIGNMENT_DESCRIPTION %}border-info{% endif %}">
                        <div class="card-body p-3">
                            {% if 'alanı' in assignment.ASSIGNMENT_DESCRIPTION %}
                            <!-- Alan güncelleme kaydı -->
                            <div class="d-flex flex-wrap align-items-center mb-2">
                                <div class="me-3 mb-1">
                                    <i class="bi bi-calendar-date me-1"></i>
                                    <span class="text-muted small">{{ assignment.ASSIGNMENT_DATE|format_date }}</span>
                                </div>
                                <div class="me-3 mb-1">
                                    <i class="bi bi-pencil-square me-1"></i>
                                    <span class="badge bg-info me-1">Alan Güncelleme</span>
                                </div>
                                <div class="mb-1">
                                    <i class="bi bi-person me-1"></i>
                                    <span>{{ assignment.assigned_by_name if assignment.assigned_by_name else 'Bilinmiyor' }} tarafından</span>
                                </div>
                            </div>
                            <div class="mt-1">
                                <strong>İşlem:</strong> {{ assignment.ASSIGNMENT_DESCRIPTION }}
                            </div>
                            {% else %}
                            <!-- Normal atama kaydı -->
                            <div class="d-flex flex-wrap align-items-center mb-2">
                                <div class="me-3 mb-1">
                                    <i class="bi bi-calendar-date me-1"></i>
                                    <span class="text-muted small">{{ assignment.ASSIGNMENT_DATE|format_date }}</span>
                                </div>
                                <div class="me-3 mb-1">
                                    <i class="bi bi-person me-1"></i>
                                    <strong>Atayan:</strong> {{ assignment.assigned_by_name if assignment.assigned_by_name else 'Bilinmiyor' }}
                                </div>
                                <div class="me-3 mb-1">
                                    <i class="bi bi-person-check me-1"></i>
                                    <strong>Atanan:</strong> {{ assignment.assigned_to_name if assignment.assigned_to_name else 'Bilinmiyor' }}
                                </div>
                                <div class="mb-1">
                                    <i class="bi bi-flag me-1"></i>
                                    <span class="badge {% if assignment.STATUS == 'BEKLEMEDE' %}bg-warning{% elif assignment.STATUS == 'TAMAMLANDI' %}bg-success{% elif assignment.STATUS == 'REDDEDILDI' %}bg-danger{% else %}bg-primary{% endif %}">
                                        {{ assignment.STATUS }}
                                    </span>
                                </div>
                            </div>
                            {% if assignment.ASSIGNMENT_DESCRIPTION %}
                            <div class="border-top pt-2 mt-2">
                                <i class="bi bi-chat-left-text me-1"></i>
                                <strong>Yorum:</strong> {{ assignment.ASSIGNMENT_DESCRIPTION }}
                            </div>
                            {% endif %}
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted">Henüz atama geçmişi bulunmamaktadır.</p>
                {% endif %}
            </div>
        </div>

        {% if pictures %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Resimler</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for pic in pictures %}
                    <div class="col-md-4 mb-3">
                        <div class="image-card" onclick="openImage('{{ url_for('get_image', filename=pic.picture) }}')">
                            <div class="image-container">
                                <img src="{{ pic.picture|get_thumbnail_path }}" class="thumbnail-img" alt="Form Resmi"
                                     title="Büyük resmi görmek için tıklayın">
                                <div class="image-overlay">
                                    <div class="overlay-icon">
                                        <i class="bi bi-zoom-in"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="image-info">
                                <span class="image-number">Resim {{ loop.index }}</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        {% if attachments %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Dosya Ekleri</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for attachment in attachments %}
                    <div class="col-md-4 mb-3">
                        <div class="file-card" onclick="openFile('{{ url_for('download_file', filename=attachment.STORED_FILENAME) }}')">
                            <div class="file-container">
                                {% if attachment.ORIGINAL_FILENAME.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) %}
                                <div class="file-image-container">
                                    <img src="{{ url_for('download_file', filename=attachment.STORED_FILENAME) }}" class="file-thumbnail" alt="{{ attachment.ORIGINAL_FILENAME }}">
                                    <div class="file-overlay">
                                        <div class="overlay-icon">
                                            <i class="bi bi-download"></i>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <div class="file-icon-container">
                                    {% if attachment.ORIGINAL_FILENAME.lower().endswith(('.pdf')) %}
                                    <i class="bi bi-file-pdf file-icon text-danger"></i>
                                    {% elif attachment.ORIGINAL_FILENAME.lower().endswith(('.doc', '.docx')) %}
                                    <i class="bi bi-file-word file-icon text-primary"></i>
                                    {% elif attachment.ORIGINAL_FILENAME.lower().endswith(('.xls', '.xlsx')) %}
                                    <i class="bi bi-file-excel file-icon text-success"></i>
                                    {% else %}
                                    <i class="bi bi-file-earmark file-icon text-secondary"></i>
                                    {% endif %}
                                    <div class="file-overlay">
                                        <div class="overlay-icon">
                                            <i class="bi bi-download"></i>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            <div class="file-info">
                                <span class="file-name" title="{{ attachment.ORIGINAL_FILENAME }}">{{ attachment.ORIGINAL_FILENAME }}</span>
                                <span class="file-meta">{{ (attachment.FILE_SIZE / 1024)|round(1) }} KB</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <style>
            /* Resim ve Dosya Kartları için Ortak Stiller */
            .image-card, .file-card {
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 6px 12px rgba(0,0,0,0.1);
                transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
                background-color: #fff;
                height: 100%;
                border: 1px solid rgba(0,0,0,0.05);
                cursor: pointer;
                position: relative;
            }

            .image-card:hover, .file-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 12px 24px rgba(0,0,0,0.15);
            }

            /* Resim Kartları için Stiller */
            .image-container {
                position: relative;
                overflow: hidden;
                padding-top: 75%; /* 4:3 aspect ratio */
            }

            .thumbnail-img {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                cursor: pointer;
                transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
            }

            .image-card:hover .thumbnail-img {
                transform: scale(1.08);
            }

            /* Dosya Kartları için Stiller */
            .file-container {
                position: relative;
                overflow: hidden;
                padding-top: 75%; /* 4:3 aspect ratio */
                background-color: #f8f9fa;
            }

            /* Dosya İkon Konteyneri */
            .file-icon-container {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .file-icon {
                font-size: 4rem;
                transition: transform 0.3s ease;
            }

            .file-card:hover .file-icon {
                transform: scale(1.1);
            }

            /* Dosya Resim Konteyneri */
            .file-image-container {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
            }

            .file-thumbnail {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
            }

            .file-card:hover .file-thumbnail {
                transform: scale(1.08);
            }

            /* Overlay Stiller */
            .image-overlay, .file-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.5));
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
                z-index: 10;
                pointer-events: none; /* Tıklamaları alttaki elemanlara geçir */
            }

            .image-card:hover .image-overlay, .file-card:hover .file-overlay {
                opacity: 1;
            }

            .overlay-icon {
                color: white;
                font-size: 2.5rem;
                background-color: rgba(0,0,0,0.3);
                border-radius: 50%;
                width: 60px;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
                transform: scale(0.8);
                transition: transform 0.3s ease;
            }

            .image-card:hover .overlay-icon, .file-card:hover .overlay-icon {
                transform: scale(1);
            }

            /* Bilgi Alanları */
            .image-info, .file-info {
                padding: 12px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background-color: #f8f9fa;
                border-top: 1px solid #eee;
            }

            .image-date, .file-meta {
                font-size: 0.85rem;
                color: #6c757d;
                font-weight: 500;
            }

            .image-number {
                font-size: 0.85rem;
                color: #495057;
                font-weight: 600;
                background-color: #e9ecef;
                padding: 2px 8px;
                border-radius: 4px;
            }

            .file-name {
                font-size: 0.85rem;
                color: #495057;
                font-weight: 600;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 70%;
            }
        </style>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Durum Güncelle</h5>
            </div>
            <div class="card-body">
                <form method="post" action="{{ url_for('form_update', form_id=form.ID) }}" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="status" class="form-label">Durum</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="BEKLEMEDE" {% if form.status == 'BEKLEMEDE' %}selected{% endif %}>BEKLEMEDE</option>
                            <option value="INCELENIYOR" {% if form.status == 'INCELENIYOR' %}selected{% endif %}>İNCELENİYOR</option>
                            <option value="COZULDU" {% if form.status == 'COZULDU' %}selected{% endif %}>ÇÖZÜLDÜ</option>
                            <option value="TAMAMLANDI" {% if form.status == 'TAMAMLANDI' %}selected{% endif %}>TAMAMLANDI</option>
                            <option value="REDDEDILDI" {% if form.status == 'REDDEDILDI' %}selected{% endif %}>REDDEDİLDİ</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="assigned_to" class="form-label">Atanan Kişi</label>
                        <select class="form-select" id="assigned_to" name="assigned_to" required>
                            {% for user in users %}
                            <option value="{{ user.ID }}" {% if last_assigned_to and user.ID == last_assigned_to %}selected{% endif %}>
                                {{ user.NAME }} {{ user.SURNAME }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="comments" class="form-label">Yorumlar</label>
                        <textarea class="form-control" id="comments" name="comments" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="attachments" class="form-label">Dosya Ekle</label>
                        <input type="file" class="form-control" id="attachments" name="attachments" multiple>
                        <div class="form-text">Desteklenen dosya türleri: PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, PNG, TXT. Maksimum dosya boyutu: 16MB</div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Güncelle</button>
                    </div>
                </form>
            </div>
        </div>


    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Resmi yeni sekmede açan fonksiyon
    function openImage(url) {
        window.open(url, '_blank');
    }

    // Dosyayı yeni sekmede açan fonksiyon
    function openFile(url) {
        window.open(url, '_blank');
    }

    // Yazdırma fonksiyonu
    function printForm() {
        window.print();
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Yazdırma butonu için olay dinleyicisi ekle
        const printButton = document.getElementById('printButton');
        if (printButton) {
            printButton.addEventListener('click', printForm);
        }

        // Form tipi seçenekleri
        let formTypes = [];

        // Sayfa yüklendiğinde form tiplerini al
        {% if form_types %}
        // Sunucudan gelen form tipleri
        formTypes = [
            {% for type in form_types %}
            "{{ type.DISPLAY }}",
            {% endfor %}
        ];
        {% else %}
        // Form tipleri yoksa API'den al
        fetch('/api/form-types')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.form_types) {
                    formTypes = data.form_types.map(type => type.DISPLAY);
                }
            })
            .catch(error => {
                console.error('Form tipleri alınırken hata oluştu:', error);
            });
        {% endif %}

        // Düzenlenebilir alanları seç
        const editableFields = document.querySelectorAll('.editable-field[data-editable="true"]');

        // Her düzenlenebilir alan için çift tıklama olayı ekle
        editableFields.forEach(field => {
            field.addEventListener('dblclick', function() {
                // Zaten düzenleme modunda ise çık
                if (field.classList.contains('editing')) return;

                // Düzenleme moduna geç
                field.classList.add('editing');

                // Mevcut değeri al
                const currentValue = field.textContent.trim();

                // Alan tipine göre düzenleme arayüzü oluştur
                if (field.dataset.field === 'TYPE') {
                    // Form tipi için dropdown menü oluştur
                    const select = document.createElement('select');
                    select.className = 'editable-field-select';

                    // Seçenekleri ekle
                    formTypes.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type;
                        option.textContent = type;
                        if (type === currentValue) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });

                    // Mevcut içeriği temizle ve select elementini ekle
                    field.textContent = '';
                    field.appendChild(select);

                    // Odaklan
                    select.focus();

                    // Blur olayı ekle
                    select.addEventListener('blur', function() {
                        saveField(field, select.value);
                    });

                    // Değişiklik olayı ekle
                    select.addEventListener('change', function() {
                        saveField(field, select.value);
                    });
                } else {
                    // Metin alanı için input oluştur
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.className = 'editable-field-input';
                    input.value = currentValue;

                    // Mevcut içeriği temizle ve input elementini ekle
                    field.textContent = '';
                    field.appendChild(input);

                    // Odaklan ve tüm metni seç
                    input.focus();
                    input.select();

                    // Blur olayı ekle
                    input.addEventListener('blur', function() {
                        saveField(field, input.value);
                    });

                    // Enter tuşu olayı ekle
                    input.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            saveField(field, input.value);
                        }
                    });
                }
            });
        });

        // Alanı kaydet
        function saveField(field, newValue) {
            // Düzenleme modundan çık
            field.classList.remove('editing');

            // Değer değişmediyse sadece içeriği geri yükle
            if (newValue === field.dataset.originalValue) {
                field.textContent = newValue;
                return;
            }

            // Değeri güncelle
            const formId = field.dataset.formId;
            const fieldName = field.dataset.field;

            // AJAX isteği gönder
            fetch(`/api/forms/${formId}/update-field`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    field: fieldName,
                    value: newValue
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Başarılı olursa sayfayı yenile
                    showNotification('Alan başarıyla güncellendi', 'success');
                    // 1 saniye bekleyip sayfayı yenile
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    // Hata durumunda eski değeri geri yükle
                    field.textContent = field.dataset.originalValue || '';
                    // Hata bildirimi göster
                    showNotification('Alan güncellenirken bir hata oluştu: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Hata:', error);
                // Hata durumunda eski değeri geri yükle
                field.textContent = field.dataset.originalValue || '';
                // Hata bildirimi göster
                showNotification('Alan güncellenirken bir hata oluştu', 'danger');
            });
        }

        // Bildirim göster
        function showNotification(message, type) {
            // Bootstrap toast kullanarak bildirim göster
            const toastContainer = document.createElement('div');
            toastContainer.className = 'position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '1050';

            const toastHtml = `
                <div class="toast align-items-center text-white bg-${type}" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;

            toastContainer.innerHTML = toastHtml;
            document.body.appendChild(toastContainer);

            const toastElement = toastContainer.querySelector('.toast');
            const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
            toast.show();

            // Toast kapandığında container'ı kaldır
            toastElement.addEventListener('hidden.bs.toast', function() {
                document.body.removeChild(toastContainer);
            });
        }

        // Sayfa yüklendiğinde orijinal değerleri kaydet
        editableFields.forEach(field => {
            field.dataset.originalValue = field.textContent.trim();
        });
    });
</script>
{% endblock %}