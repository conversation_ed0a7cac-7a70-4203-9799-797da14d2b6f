# ISG Form Yönetim Sistemi Kullanıcı Kılavuzu

## İçindekiler

1. [<PERSON><PERSON><PERSON>](#giriş)
2. [<PERSON><PERSON><PERSON>](#sisteme-giriş)
3. [Kullanıcı Arayüzü](#kullanıcı-arayüzü)
4. [Form İşlemleri](#form-işlemleri)
   - [Form Listeleme](#form-listeleme)
   - [Form Oluşturma](#form-oluşturma)
   - [Form Detayları](#form-detayları)
   - [Form Durumu Güncelleme](#form-durumu-güncelleme)
   - [Form Alanlarını Düzenleme](#form-alanlarını-düzenleme)
5. [Resim İşlemleri](#resim-işlemleri)
   - [Resimleri Görüntüleme](#resimleri-görüntüleme)
6. [Admin Fonksiyonları](#admin-fonksiyonları)
   - [Kullanıc<PERSON> Yönetimi](#kullanıcı-yönetimi)
   - [<PERSON>llan<PERSON><PERSON><PERSON> Ekleme](#kullanıcı-ekleme)
   - [Kullanıcı Durumu Değiştirme](#kullanıcı-durumu-değiştirme)
   - [Kullanıcı Şifre Sıfırlama](#kullanıcı-şifre-sıfırlama)
   - [Sistem Logları](#sistem-logları)
7. [Çıkış](#çıkış)
8. [Kullanıcı Yetkileri ve Özellikleri](#kullanıcı-yetkileri-ve-özellikleri)
9. [Teknik Bilgiler](#teknik-bilgiler)

## Giriş

ISG Form Yönetim Sistemi, iş sağlığı ve güvenliği formlarının dijital ortamda yönetilmesini sağlayan bir web uygulamasıdır. Bu sistem, formların oluşturulması, takibi, güncellenmesi ve raporlanması işlemlerini kolaylaştırır.

## Sisteme Giriş

1. Web tarayıcınızda ISG Form Yönetim Sistemi'nin adresine gidin.
2. Karşınıza gelen giriş ekranında kullanıcı adı ve şifrenizi girin.
3. "Giriş Yap" butonuna tıklayın.

**Not:** Eğer hesabınız devre dışı bırakılmışsa veya "NULL" yetkisine sahipse sisteme giriş yapamazsınız. Bu durumda sistem yöneticinizle iletişime geçin.

## Kullanıcı Arayüzü

Sisteme giriş yaptıktan sonra, üst menüde aşağıdaki seçenekleri göreceksiniz:

- **Yönetim:** Kontrol paneli ve sistem loglarına erişim sağlar (Sistem logları sadece admin kullanıcılar için görünür)
- **Formlar:** Tüm formların listelendiği sayfaya yönlendirir
- **Yeni Form:** Yeni bir form oluşturmanızı sağlar
- **Kullanıcı Yönetimi:** Kullanıcıları yönetmenizi sağlar (Sadece admin kullanıcılar için görünür)

Sağ üst köşede kullanıcı adınız ve yetki seviyeniz görüntülenir. Buradan çıkış yapabilirsiniz.

## Form İşlemleri

### Form Listeleme

Formlar sayfasında, sistemdeki tüm formları görebilirsiniz. Bu sayfada:

1. Formları filtreleyebilirsiniz (Form tipi, durum, tarih aralığı vb.)
2. Arama yapabilirsiniz
3. Form detaylarını görüntüleyebilirsiniz

### Form Oluşturma

Yeni bir form oluşturmak için:

1. Üst menüden "Yeni Form" seçeneğine tıklayın
2. Form tipini seçin
3. Gerekli alanları doldurun
4. "Formu Oluştur" butonuna tıklayın

### Form Detayları

Form detay sayfasında, formun tüm bilgilerini görebilir ve işlem yapabilirsiniz:

1. Formun genel bilgileri (ID, oluşturma tarihi, durum vb.)
2. Form içeriği
3. Form resimleri
4. Form atama geçmişi

### Form Durumu Güncelleme

Form durumunu güncellemek için:

1. Form detay sayfasında "Durum Güncelle" butonuna tıklayın
2. Yeni durumu seçin
3. Atanacak kişiyi seçin
4. Açıklama girin
5. "Güncelle" butonuna tıklayın

### Form Alanlarını Düzenleme

Admin kullanıcılar, form alanlarını doğrudan düzenleyebilirler:

1. Form detay sayfasında düzenlenebilir alanlara çift tıklayın (Form tipi, olay yeri)
2. Açılan düzenleme alanında değişikliği yapın
3. Enter tuşuna basın veya başka bir alana tıklayın

**Not:** Bu özellik sadece admin kullanıcılar için geçerlidir.

## Resim İşlemleri

### Resimleri Görüntüleme

Form detay sayfasında, forma ait resimleri görüntüleyebilirsiniz:

1. Küçük resim (thumbnail) görüntüleri listelenir
2. Resme tıklayarak büyük halini yeni sekmede açabilirsiniz
3. Resim üzerine geldiğinizde zoom ikonu görünür

## Admin Fonksiyonları

### Kullanıcı Yönetimi

Admin kullanıcılar, "Kullanıcı Yönetimi" sayfasından tüm kullanıcıları yönetebilirler:

1. Kullanıcıları listeleme
2. Yeni kullanıcı ekleme
3. Kullanıcı durumunu değiştirme (aktif/pasif)
4. Kullanıcı şifresini sıfırlama

### Kullanıcı Ekleme

Yeni bir kullanıcı eklemek için:

1. Kullanıcı Yönetimi sayfasında "Yeni Kullanıcı Ekle" butonuna tıklayın
2. Gerekli bilgileri girin:
   - Ad
   - Soyad
   - Kullanıcı Adı
   - Şifre
   - E-posta
   - Yetki (ADMIN, USER, NULL)
3. "Kullanıcı Oluştur" butonuna tıklayın

**Not:** NULL yetkisine sahip kullanıcılar sisteme giriş yapamazlar.

### Kullanıcı Durumu Değiştirme

Kullanıcı durumunu değiştirmek için:

1. Kullanıcı listesinde ilgili kullanıcının yanındaki "Durum Değiştir" butonuna tıklayın
2. Onay penceresinde "Evet" butonuna tıklayın

Pasif durumdaki kullanıcılar sisteme giriş yapamazlar.

### Kullanıcı Şifre Sıfırlama

Kullanıcı şifresini sıfırlamak için:

1. Kullanıcı listesinde ilgili kullanıcının yanındaki "Şifre Sıfırla" butonuna tıklayın
2. Yeni şifreyi girin
3. "Şifreyi Güncelle" butonuna tıklayın

### Sistem Logları

Admin kullanıcılar, sistem loglarını görüntüleyebilirler:

1. Üst menüden "Yönetim" > "Sistem Logları" seçeneğine tıklayın
2. Logları filtreleyebilirsiniz:
   - Log türü (giriş, çıkış, form oluşturma vb.)
   - Kullanıcı
   - Tarih aralığı
   - Gösterilecek kayıt sayısı
3. Arama yapabilirsiniz

Sistem logları, aşağıdaki işlemleri kaydeder:

- **LOGIN:** Kullanıcı girişi
- **LOGOUT:** Kullanıcı çıkışı
- **FORM_CREATE:** Form oluşturma
- **FORM_UPDATE:** Form güncelleme
- **FORM_ASSIGNMENT:** Form atama
- **USER_CREATE:** Kullanıcı oluşturma
- **USER_UPDATE:** Kullanıcı güncelleme
- **USER_STATUS:** Kullanıcı durumu değiştirme
- **USER_PASSWORD:** Kullanıcı şifre sıfırlama
- **FIELD_UPDATE:** Form alanı güncelleme
- **ERROR:** Hata durumları

## Çıkış

Sistemden çıkış yapmak için:

1. Sağ üst köşede kullanıcı adınızın yanındaki menüye tıklayın
2. "Çıkış Yap" seçeneğine tıklayın

## Kullanıcı Yetkileri ve Özellikleri

### Yetki Türleri

Sistemde üç farklı yetki türü bulunmaktadır:

1. **ADMIN:** Tüm özelliklere erişim sağlar
2. **USER:** Temel form işlemlerine erişim sağlar
3. **NULL:** Sisteme giriş yapamaz

### ADMIN Kullanıcı Özellikleri

ADMIN kullanıcılar aşağıdaki tüm işlemleri yapabilirler:

- Tüm formları görüntüleme ve filtreleme
- Yeni form oluşturma
- Form durumunu güncelleme
- Form alanlarını düzenleme (form tipi, olay yeri)
- Kullanıcı yönetimi
  - Kullanıcı listeleme
  - Yeni kullanıcı ekleme
  - Kullanıcı durumunu değiştirme
  - Kullanıcı şifresini sıfırlama
- Sistem loglarını görüntüleme ve filtreleme

### USER Kullanıcı Özellikleri

USER kullanıcılar aşağıdaki işlemleri yapabilirler:

- Tüm formları görüntüleme ve filtreleme
- Yeni form oluşturma
- Form durumunu güncelleme

### NULL Kullanıcı Özellikleri

NULL yetkisine sahip kullanıcılar sisteme giriş yapamazlar. Bu yetki, geçici olarak erişimi kısıtlamak veya henüz yetki atanmamış kullanıcılar için kullanılır.

## Teknik Bilgiler

### Dosya Depolama

- Formlarla ilgili resimler `C:\BAYRAKTAR\ISG\MOBILE\pic\` dizininde saklanır
- Küçük resimler (thumbnails) `C:\BAYRAKTAR\ISG\MOBILE\pic\thumbnails` dizininde saklanır

### Veritabanı Tabloları

Sistem aşağıdaki ana veritabanı tablolarını kullanır:

- **USERS:** Kullanıcı bilgileri
- **ISG_FORMS:** Form bilgileri
- **ISG_PICTURE:** Form resimleri
- **FORM_ASSIGNMENTS:** Form atama geçmişi
- **USER_LOGS:** Sistem logları
- **FORM_TYPE:** Form tipleri

---

Bu kullanıcı kılavuzu, ISG Form Yönetim Sistemi'nin tüm özelliklerini kapsamaktadır. Herhangi bir sorunuz veya öneriniz varsa, lütfen sistem yöneticinizle iletişime geçin.
