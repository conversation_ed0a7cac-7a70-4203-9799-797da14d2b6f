{% extends 'base.html' %}

{% block title %}Formlar - ISG Form Yönetim Sistemi{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="mb-1">Formlar</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">Kontrol Paneli</a></li>
                        <li class="breadcrumb-item active">Formlar</li>
                    </ol>
                </nav>
            </div>
            <div>
                {% if is_admin %}
                <span class="badge bg-danger me-2">Tüm Formlar</span>
                {% else %}
                <span class="badge bg-info me-2">Size Atanmış Tüm Formlar</span>
                {% endif %}
                <a href="{{ url_for('form_create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>Yeni Form
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6 mb-2 mb-md-0">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-list-check me-2 text-primary"></i>
                            <h5 class="mb-0">Form Listesi</h5>
                            <span class="badge bg-primary rounded-pill ms-2">{{ forms|length if forms else 0 }}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="bi bi-search text-muted"></i>
                            </span>
                            <input type="text" id="searchInput" class="form-control border-start-0" placeholder="Ara...">
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if forms %}
                <div class="table-responsive">
                    <table class="table table-hover" id="formsTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Tür</th>
                                <th>Tarih</th>
                                <th>Kullanıcı</th>
                                <th>Olay Yeri</th>
                                <th>Durum</th>
                                <th>Atanan Kişi</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for form in forms %}
                            <tr>
                                <td>
                                    <span class="fw-medium">{{ form.ID }}</span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">
                                            {% if form.TYPE == 'Ramak Kala' %}
                                            <i class="bi bi-exclamation-triangle text-warning"></i>
                                            {% elif form.TYPE == 'Kaza' %}
                                            <i class="bi bi-bandaid text-danger"></i>
                                            {% elif form.TYPE == 'Öneri' %}
                                            <i class="bi bi-lightbulb text-primary"></i>
                                            {% else %}
                                            <i class="bi bi-file-text text-secondary"></i>
                                            {% endif %}
                                        </span>
                                        {{ form.TYPE }}
                                    </div>
                                </td>
                                <td>{{ form.date|format_date }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="avatar-sm bg-light text-primary me-2">
                                            {{ form.user_fullname[:1] if form.user_fullname else '?' }}
                                        </span>
                                        <span>{{ form.user_fullname if form.user_fullname else 'Bilinmiyor' }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-truncate d-inline-block" style="max-width: 150px;" title="{{ form.incident_loc }}">
                                        {{ form.incident_loc }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge {% if form.status == 'BEKLEMEDE' %}bg-warning{% elif form.status == 'TAMAMLANDI' %}bg-success{% elif form.status == 'COZULDU' %}bg-success{% elif form.status == 'REDDEDILDI' %}bg-danger{% else %}bg-primary{% endif %}">
                                        {{ form.status }}
                                    </span>
                                </td>
                                <td>
                                    {% if form.assigned_to_name %}
                                    <div class="d-flex align-items-center">
                                        <span class="avatar-sm bg-light text-info me-2">
                                            {{ form.assigned_to_name[:1] if form.assigned_to_name else '?' }}
                                        </span>
                                        <span class="text-truncate d-inline-block" style="max-width: 100px;" title="{{ form.assigned_to_name }}">
                                            {{ form.assigned_to_name }}
                                        </span>
                                    </div>
                                    {% else %}
                                    <span class="badge bg-secondary">Atanmamış</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('form_detail', form_id=form.ID) }}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-eye me-1"></i>Görüntüle
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-3">Henüz form bulunmamaktadır.</p>
                    <a href="{{ url_for('form_create') }}" class="btn btn-primary mt-2">
                        <i class="bi bi-plus-circle me-2"></i>Yeni Form Oluştur
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Form arama işlevi main.js dosyasında tanımlanmıştır -->
{% endblock %}
